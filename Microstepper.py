import sys, toupcam, cv2, serial, time, serial.tools.list_ports, math
from PyQt5.QtCore import pyqtSignal, pyqtSlot, QTimer, QSignalBlocker, Qt, QRect, QSize
from PyQt5.QtGui import QPixmap, QImage, QFont, QPainter, QPen
from PyQt5.QtWidgets import (QLineEdit,QTextEdit,QSlider, QSpinBox,QLabel, QApplication, QWidget, QCheckBox, QPushButton, QComboBox, QSlider, QGraphicsView, QGraphicsScene, QGraphicsPixmapItem,QFileDialog,
                             QGroupBox,QButtonGroup, QVBoxLayout, QHBoxLayout, QGridLayout, QTabWidget, QSizePolicy, QMessageBox, QMenu, QAction, QInputDialog, QScrollArea, QStackedWidget, QDialog)
import keyboard, os
import re
import serial.tools.list_ports
from serial.serialutil import SerialException
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime 
import subprocess
import cv2, glob
from pyqt_vertical_tab_widget.verticalTabWidget import VerticalTabWidget

key_status = {}

class ImageStreamWidget(QLabel):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.main_widget = parent
        self.setMouseTracking(True)
        if self.main_widget :
            self.main_widget.setMouseTracking(True)

        # Kamera Preview Setting
        self.cap = None
        self.image_width = 1920
        self.image_height = 1080
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.update_image)
        
        # Frame Preview Setting
        self.current_frame = None
        self.cropped_image = None
        self.is_showing_crop = False
        self.frozen_frame = None  # Frame to hold the frozen image
        self.is_frozen = False  # Flag to indicate if the image is frozen
        self.frozen_machine_position = None  # Position of the machine when frozen

        # Pointer
        self.pointer_size = 20
        self.pointer_color = (0, 255, 0)
        self.show_pointer = True
        
        # ROI
        self.roi_start = None
        self.roi_end = None
        self.is_selecting_roi = False
        self.roi_color = (0,0,255)  # Color for ROI rectangle
        self.roi_thickness = 2  # Thickness of the ROI rectangle

        # Snap
        self.snap_folder = "snap_images"  # Folder to save snapshot images
        if not os.path.exists(self.snap_folder):
            os.makedirs(self.snap_folder)
        self.snap_count = 0

        # Move To
        self.move_to_mode = False  # Flag to indicate if move to mode is active
        self.move_to_thickness = 2  # Thickness of the move to ROI rectangle
        self.move_to_point = None
        self.move_to_roi_size = (41,41)
        self.move_to_color = (0,0,255)  # Color for move to ROI rectangle
        self.move_to_thickness = 2  # Thickness of the move to ROI rectangle
        self.machine_position = (0, 0, 0)  # Initial machine position (X, Y, Z)
        self.pixel_per_mm = 36.571428571428  # Pixels per millimeter for conversion

    # === Preview Camera ===
    def start_stream(self):
        if not self.cap:
            self.cap = cv2.VideoCapture(0, cv2.CAP_MSMF)  # Ganti 0 dengan indeks kamera USB Anda jika ada lebih dari satu kamera
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.image_width)  # Mengatur lebar frame ke 1920 piksel
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.image_height)  # Mengatur tinggi frame ke 1080 piksel
            self.timer.start(1000 // 30)  # Stream at 30 FPS
            self.setMouseTracking(True)

    def stop_stream(self):
        try:
            if self.cap:
                self.timer.stop()
                self.cap.release()
                self.cap = None
                # Jangan hapus current_frame karena masih dibutuhkan untuk crop
        except Exception as e:
            print(f"Error in stop_stream: {e}")
    
    def restart_stream(self):
        """Restart kamera stream"""
        try:
            if self.is_showing_crop:
                self.is_showing_crop = False
                self.cropped_image = None  # Hapus referensi crop
                self.current_frame = None  # Reset frame
                self.start_stream()
        except Exception as e:
            print(f"Error in restart_stream: {e}")
    
    # === Frame Kamera ===
    def update_image(self):
        if not self.cap or self.is_showing_crop:
            return

        if not self.is_frozen:
            ret, frame = self.cap.read()
            if ret:
                self.current_frame = frame.copy()
                frame = self.current_frame.copy()
        else: 
            if self.frozen_frame is not None:
                self.current_frame = self.frozen_frame.copy()
                frame = self.frozen_frame.copy()
            else:
                return
             
        frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        height, width, channel = frame.shape

        if self.show_pointer:
            center_x = width // 2
            center_y = height // 2
            
            line_length = self.pointer_size // 2

            cv2.line(frame, 
                     (center_x - line_length, center_y), 
                     (center_x + line_length, center_y), 
                     self.pointer_color, 2)

            cv2.line(frame, 
                     (center_x, center_y - line_length), 
                     (center_x, center_y + line_length), 
                     self.pointer_color, 2)
            
            # Gambar ROI jika ada
        if self.roi_start and (self.roi_end or self.is_selecting_roi):
            start_x, start_y = self.roi_start
            if self.roi_end:
                end_x, end_y = self.roi_end
            else:
                # Untuk preview saat dragging
                mouse_pos = self.mapFromGlobal(self.cursor().pos())
                scaled_width = self.pixmap().width()
                scaled_height = self.pixmap().height()
                x_offset = (self.width() - scaled_width) // 2
                y_offset = (self.height() - scaled_height) // 2
                adjusted_x = mouse_pos.x() - x_offset
                adjusted_y = mouse_pos.y() - y_offset
                end_x = int(adjusted_x * self.image_width / scaled_width)
                end_y = self.image_height - int(adjusted_y * self.image_height / scaled_height)
        
            # Konversi koordinat Y untuk penggambaran
            display_start_y = self.image_height - start_y
            display_end_y = self.image_height - end_y
                
            cv2.rectangle(frame,
                        (start_x, display_start_y),
                        (end_x, display_end_y),
                        self.roi_color,
                        self.roi_thickness)
        
        # Update Move To ROI dengan koordinat yang dikonversi
        if self.move_to_point and self.move_to_mode:
            x, y = self.move_to_point
            # Konversi koordinat Y untuk penggambaran
            display_y =  self.image_height - y
            
            half_width = self.move_to_roi_size[0] // 2
            half_height = self.move_to_roi_size[1] // 2
            
            roi_x1 = max(0, x - half_width)
            # Konversi koordinat Y untuk penggambaran
            display_start_y = self.image_height - start_y
            display_end_y = self.image_height - end_y
                
            cv2.rectangle(frame,
                        (start_x, display_start_y),
                        (end_x, display_end_y),
                        self.roi_color,
                        self.roi_thickness)
        
        # Update Move To ROI dengan koordinat yang dikonversi
        if self.move_to_point and self.move_to_mode:
            x, y = self.move_to_point
            # Konversi koordinat Y untuk penggambaran
            display_y =  self.image_height - y
            
            half_width = self.move_to_roi_size[0] // 2
            half_height = self.move_to_roi_size[1] // 2
            
            roi_x1 = max(0, x - half_width)
            roi_y1 = max(0, display_y - half_height)
            roi_x2 = min(width, x + half_width)
            roi_y2 = min(height, display_y + half_height)

            cv2.rectangle(frame,
                        (roi_x1, roi_y1),
                        (roi_x2, roi_y2),
                        self.move_to_color,
                        self.move_to_thickness)
            
        bytes_per_line = channel * width
        qimage = QImage(frame.data, width, height, bytes_per_line, QImage.Format_RGB888)
        pixmap = QPixmap.fromImage(qimage)
        
        # Sesuaikan ukuran berdasarkan ukuran widget saat ini
        scaled_pixmap = pixmap.scaled(self.width(), self.height(), Qt.KeepAspectRatio)
        self.setPixmap(scaled_pixmap)
    
    def crop_and_display_roi(self):
        if self.roi_start and self.roi_end:
            try:    
                # Dapatkan koordinat ROI
                x1, y1 = self.roi_start
                x2, y2 = self.roi_end
                
                # Pastikan koordinat dalam urutan yang benar
                x_min, x_max = min(x1, x2), max(x1, x2)

                cv_y1 = self.image_height - y1
                cv_y2 = self.image_height - y2
                # Convert coordinates for OpenCV (flip Y axis)
                y_min = min(cv_y1, cv_y2)
                y_max = max(cv_y1, cv_y2)
                
                # Debug prints
                print(f"Current frame shape: {self.current_frame.shape if self.current_frame is not None else 'None'}")
                
                if self.current_frame is not None:
                    # Validate coordinates are within image bounds
                    height, width = self.current_frame.shape[:2]
                    x_min = max(0, min(x_min, width-1))
                    x_max = max(0, min(x_max, width-1))
                    y_min = max(0, min(y_min, height-1))
                    y_max = max(0, min(y_max, height-1))

                    self.roi_width = x_max - x_min
                    self.roi_height = abs(y_max - y_min)
                    self.roi_offset_x = x_min
                    self.roi_offset_y = min(y_min, y_max)
                        
                        # Crop the image
                    self.cropped_image = self.current_frame[y_min:y_max, x_min:x_max].copy()

                    if self.cropped_image is not None and self.cropped_image.size > 0:
                        self.is_showing_crop = True
                        self.update_crop_display()
                        if hasattr(self.main_widget, 'lbl_roi_size'):
                            self.main_widget.lbl_roi_size.setText(f"ROI Size            : {self.roi_width} x {self.roi_height}")
            except Exception as e:
                print(f"Error in crop_and_display_roi: {e}")
                self.restart_stream()
    
    def freeze_image(self, state):
        """Freeze atau unfreeze gambar stream"""
        self.is_frozen = state
        if state and self.current_frame is not None:
            # Simpan frame dan posisi mesin saat ini
            self.frozen_frame = self.current_frame.copy()
            self.frozen_machine_position = self.machine_position
        else:
            # Reset frozen frame dan posisi saat unfreeze
            self.frozen_frame = None
            self.frozen_machine_position = None
    
    # === ROI ===
    def show_move_to_roi(self, x, y):
        """Tampilkan ROI untuk titik yang akan dituju"""
        if self.is_showing_crop:
            # Simpan koordinat asli untuk move to
            self.move_to_point = (x, y)
        else:
            # Balik koordinat Y untuk frame penuh
            y = self.image_height - y
            self.move_to_point = (x, y)
        
        self.move_to_roi_size = (41, 41)
        self.move_to_color = (0, 0, 255)  # Color for move to ROI rectangle
        self.move_to_thickness = 2  # Thickness of the move to ROI rectangle

        print(f"Move to point: ({x}, {y})")
    
        # Update tampilan
        if self.is_showing_crop:
            self.update_crop_display()
        else:
            self.update_image()

    def snap_pointer_roi(self):
        """Mengambil dan menyimpan gambar ROI 20x20 di sekitar pointer"""
        if self.current_frame is not None:
            try:
                # Dapatkan koordinat tengah pointer
                center_x = self.image_width // 2
                center_y = self.image_height // 2
                
                # Hitung koordinat ROI (10 pixel ke setiap arah dari tengah)
                roi_x1 = max(0, center_x - 250)
                roi_y1 = max(0, center_y - 250)
                roi_x2 = min(self.image_width, center_x + 250)
                roi_y2 = min(self.image_height, center_y + 250)
                
                # Crop gambar
                roi_image = self.current_frame[roi_y1:roi_y2, roi_x1:roi_x2]

                self.cropped_image = roi_image.copy()
                self.roi_width = roi_x2 - roi_x1
                self.roi_height = roi_y2 - roi_y1
                self.roi_offset_x = roi_x1
                self.roi_offset_y = roi_y1
                self.is_showing_crop = True
                self.update_crop_display()
                
                # Buat nama file dengan timestamp
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                self.snap_count += 1
                filename = f"pointer_roi_{timestamp}_{self.snap_count}.jpg"
                filepath = os.path.join(self.snap_folder, filename)
                
                # Simpan gambar
                cv2.imwrite(filepath, roi_image)
                
                print(f"Saved ROI image to: {filepath}")
                return filepath
                
            except Exception as e:
                print(f"Error in snap_pointer_roi: {e}")
                return None
    
    # === Move To ===
    def update_machine_position(self, x, y, z):
        """Update posisi mesin dari GRBL"""
        if not self.is_frozen:
            self.machine_position = (x, y, z)
            if self.frozen_machine_position is None:
                self.frozen_machine_position = (x, y, z)
        # Jika frozen, gunakan frozen_machine_position untuk perhitungan

    def calculate_machine_movement(self, click_x, click_y):
        """Hitung pergerakan mesin berdasarkan pixel yang diklik"""
        if self.current_frame is None:
            return None
        
        position_to_use = self.frozen_machine_position if self.is_frozen else self.machine_position

        if self.is_showing_crop:
            # Konversi koordinat click relatif terhadap ROI ke koordinat frame penuh
            click_x += self.roi_offset_x
            # Untuk Y, kita perlu membalik koordinatnya karena OpenCV dimulai dari atas
            click_y = (click_y + self.roi_offset_y)
            print('offset active')
        else:
            # Balik koordinat Y untuk frame penuh
            click_y =  (1080 - click_y)

        # Dapatkan ukuran frame
        height, width = self.current_frame.shape[:2]
        center_x = width // 2
        center_y = height // 2

        # Hitung perbedaan pixel dari titik tengah
        dx_pixels = click_x - center_x
        dy_pixels =- (click_y - center_y)

        # Konversi perbedaan pixel ke mm (sesuaikan dengan kalibrasi sistem)
        dx_mm = dx_pixels / self.pixel_per_mm
        dy_mm = dy_pixels / self.pixel_per_mm

        # Hitung posisi target mesins
        target_x = position_to_use[0] + 3.315 + dx_mm
        target_y = position_to_use[1] - 48.520 + dy_mm

        print(f"Click coordinates: ({click_x}, {click_y})")
        print(f"Target position: ({target_x:.3f}, {target_y:.3f})")
        print(f"Position to use: ({position_to_use[0]:.3f}, {position_to_use[1]:.3f})")
    
        return target_x, target_y
    
    def move_machine_to_point(self, click_x, click_y):
        """Gerakkan mesin ke titik yang diklik"""
        movement = self.calculate_machine_movement(click_x, click_y)
        if movement:
            target_x, target_y = movement
            # Kirim perintah ke GRBL melalui parent widget
            if hasattr(self.main_widget, 'send_grbl_command'):
                # Gunakan G0 untuk gerakan cepat
                command = f"G00 X{target_x:.3f} Y{target_y:.3f} F100"
                self.main_widget.send_grbl_command(command)
                print(f"Sending command: {command}")  # Debug print
                return True
        return False

    def mouseMoveEvent(self, event):
        if self.pixmap():  # Cek apakah ada gambar yang ditampilkan
            pos = event.pos()
            
            # Dapatkan ukuran widget dan pixmap
            widget_width = self.width()
            widget_height = self.height()
            pixmap_width = self.pixmap().width()
            pixmap_height = self.pixmap().height()
            
            # Hitung offset untuk centering
            x_offset = (widget_width - pixmap_width) // 2
            y_offset = (widget_height - pixmap_height) // 2
            
            # Kurangi offset dari posisi mouse
            adjusted_x = pos.x() - x_offset
            adjusted_y = pos.y() - y_offset
            
            # Initialize x and y with default values
            x = 0
            y = 0
            
            # Hanya proses jika mouse berada dalam area gambar
            if (0 <= adjusted_x < pixmap_width and 0 <= adjusted_y < pixmap_height):
                if self.is_showing_crop:
                    x = int(adjusted_x * self.cropped_image.shape[1] / pixmap_width)
                    y = int(adjusted_y * self.cropped_image.shape[0] / pixmap_height)
                else:
                    x = int(adjusted_x * self.image_width / pixmap_width)
                    y = self.image_height - int(adjusted_y * self.image_height / pixmap_height)
                    
                # Update label posisi di MainWidget
                if isinstance(self.main_widget, MainWidget):
                    if hasattr(self.main_widget, 'lbl_pixel_w'):
                        self.main_widget.lbl_pixel_w.setText(f"Pixel (Width)   : {x}")
                    if hasattr(self.main_widget, 'lbl_pixel_h'):
                        self.main_widget.lbl_pixel_h.setText(f"Pixel (Height)  : {y}")
                    
                # Dapatkan nilai pixel RGB
                pixel_value = self.get_pixel_value(x, y)
                
            else:
                # Reset label jika mouse di luar area gambar
                if isinstance(self.main_widget, MainWidget):
                    if hasattr(self.main_widget, 'lbl_pixel_w'):
                        self.main_widget.lbl_pixel_w.setText(f"Pixel (Width)   : -")
                    if hasattr(self.main_widget, 'lbl_pixel_h'):
                        self.main_widget.lbl_pixel_h.setText(f"Pixel (Height)  : -")
        
        super().mouseMoveEvent(event)

    def mousePressEvent(self, event):
        if self.pixmap() and self.move_to_mode:
            pos = event.pos()
            widget_width = self.width()
            widget_height = self.height()
            pixmap_width = self.pixmap().width()
            pixmap_height = self.pixmap().height()
            
            x_offset = (self.width() - pixmap_width) // 2
            y_offset = (self.height() - pixmap_height) // 2
            
            adjusted_x = pos.x() - x_offset
            adjusted_y = pos.y() - y_offset
            
            if (0 <= adjusted_x < pixmap_width and 0 <= adjusted_y < pixmap_height):
                if self.is_showing_crop:
                    x = int(adjusted_x * self.cropped_image.shape[1] / pixmap_width)
                    y = int(adjusted_y * self.cropped_image.shape[0] / pixmap_height)

                    machine_y =  y
                else:
                    x = int(adjusted_x * self.image_width / pixmap_width)
                    y = int(adjusted_y * self.image_height / pixmap_height)
                    machine_y = self.image_height - y

                self.show_move_to_roi(x, y)  # Gunakan koordinat display
                self.move_machine_to_point(x, machine_y)  # Gunakan koordinat mesin
                print(f"Clicked at: ({x}, {machine_y})")
        else:
            super().mousePressEvent(event)
            
        if self.pixmap() and hasattr(self.main_widget, 'btn_set_roi'):
            if self.main_widget.btn_set_roi.isChecked():
                pos = event.pos()
                widget_width = self.width()
                widget_height = self.height()
                pixmap_width = self.pixmap().width()
                pixmap_height = self.pixmap().height()

                x_offset = (widget_width - pixmap_width) // 2
                y_offset = (widget_height - pixmap_height) // 2

                adjusted_x = pos.x() - x_offset
                adjusted_y = pos.y() - y_offset

                if (0 <= adjusted_x < pixmap_width and 0 <= adjusted_y < pixmap_height):
                    x = int(adjusted_x * self.image_width / pixmap_width)
                    y = int(adjusted_y * self.image_height / pixmap_height)

                    y = self.image_height - y

                    self.roi_start = (x, y)
                    self.roi_machine_start = (x, y)
                    self.roi_end = None
                    self.is_selecting_roi = True
                
                if hasattr(self.main_widget, 'lbl_roi_start'):
                    self.main_widget.lbl_roi_start.setText(f"Start Position (x,y) : {x}, {y}")
                
    def mouseReleaseEvent(self, event):
        if self.is_selecting_roi and self.pixmap():
            pos = event.pos()
            widget_width = self.width()
            widget_height = self.height()
            pixmap_width = self.pixmap().width()
            pixmap_height = self.pixmap().height()

            x_offset = (widget_width - pixmap_width) // 2
            y_offset = (widget_height - pixmap_height) // 2

            adjusted_x = pos.x() - x_offset
            adjusted_y = pos.y() - y_offset

            if (0 <= adjusted_x < pixmap_width and 0 <= adjusted_y < pixmap_height):
                x = int(adjusted_x * self.image_width / pixmap_width)
                y = int(adjusted_y * self.image_height / pixmap_height)

                y = self.image_height - y
            
                self.roi_end = (x, y)
                self.is_selecting_roi = False
                
                if hasattr(self.main_widget, 'lbl_roi_end'):
                    self.main_widget.lbl_roi_end.setText(f"End Position (x,y)   : {x}, {y}")
                
            # Hitung ukuran ROI
            if self.roi_start and self.roi_end:
                width = abs(self.roi_end[0] - self.roi_start[0])
                height = abs(self.roi_end[1] - self.roi_start[1])
                if hasattr(self.main_widget, 'lbl_roi_size'):
                    self.main_widget.lbl_roi_size.setText(f"ROI Size            : {width} x {height}")
                    
    def get_pixel_value(self, x, y):
        if not self.cap:
            return None
        
        ret, frame = self.cap.read()
        if ret:
            frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            if x >= 0 and y >= 0 and x < frame.shape[1] and y < frame.shape[0]:
                color = frame[y, x]
                return (int(color[0]), int(color[1]), int(color[2]))
        return None
    
    def closeevent(self, event):
        """Handle cleanup saat widget ditutup"""
        self.stop_stream()
        self.cropped_image = None  # Cropped image from the ROI
        self.current_frame = None
        super().closeEvent(event)

    def update_crop_display(self):

        if self.is_showing_crop and self.cropped_image is not None:
            try:
                display_image = self.cropped_image.copy()
                display_image = cv2.cvtColor(display_image, cv2.COLOR_BGR2RGB)
            
                # Gambar pointer jika diaktifkan
                if self.show_pointer:
                    center_x =display_image.shape[1] // 2
                    center_y = display_image.shape[0] // 2

                    line_length = self.pointer_size // 2

                    cv2.line(display_image, 
                             (center_x - line_length, center_y), 
                             (center_x + line_length, center_y), 
                             self.pointer_color, 2)
                    
                    cv2.line(display_image, 
                             (center_x, center_y - line_length), 
                             (center_x, center_y + line_length), 
                             self.pointer_color, 2)

                if self.move_to_point and self.move_to_mode:
                    x, y = self.move_to_point

                    half_width = self.move_to_roi_size[0] // 2
                    half_height = self.move_to_roi_size[1] // 2
                    
                    # Gambar kotak hitam
                    cv2.rectangle(display_image,
                                (x - half_width, y - half_height),
                                (x + half_width, y + half_height),
                                self.move_to_color,
                                self.move_to_thickness)
                    
                    # Tambah crosshair
                    cross_size = 5
                    cv2.line(display_image,
                            (x - cross_size, y),
                            (x + cross_size, y),
                            self.move_to_color,
                            1)
                cv2.line(display_image,
                        (x, y - cross_size),
                        (x, y + cross_size),
                        self.move_to_color,
                        1)

                if hasattr(self.main_widget, 'af_point_labels'):
                    roi_width = abs(x2 - x1)
                    roi_height = abs(y2 - y1)
                    af_points = len(self.main_widget.af_point_labels)

                    for i in range(af_points):
                        # Hitung posisi AF point berdasarkan jumlah point
                        if af_points == 1:
                            # Satu titik di tengah
                            af_x = roi_width // 2
                            af_y = roi_height // 2
                        elif af_points == 2:
                            # Dua titik horizontal
                            af_x = (roi_width // 3) * (i + 1)
                            af_y = roi_height // 2
                        elif af_points == 3:
                            # Tiga titik dalam formasi segitiga
                            if i == 0:  # Titik atas tengah
                                af_x = roi_width // 2
                                af_y = roi_height // 4
                            else:  # Dua titik bawah
                                af_x = (roi_width // 3) * i
                                af_y = (roi_height * 3) // 4
                        elif af_points == 4:
                            # Empat titik di pojok
                            af_x = (roi_width // 3) * (i % 2 + 1)
                            af_y = (roi_height // 3) * (i // 2 + 1)
                        elif af_points == 5:
                            # Lima titik (4 pojok + 1 tengah)
                            if i == 4:  # Titik tengah
                                af_x = roi_width // 2
                            af_y = roi_height // 2
                        else:  # 4 titik pojok
                            af_x = (roi_width // 3) * (i % 2 + 1)
                            af_y = (roi_height // 3) * (i // 2 + 1)

                    # Gambar pointer merah untuk AF point
                    line_length = self.pointer_size // 2
                    af_color = (255, 0, 0)  # Warna merah (BGR)

                    cv2.line(cropped_rgb, 
                             (af_x - line_length, af_y), 
                             (af_x + line_length, af_y), 
                             af_color, 2)
                    cv2.line(cropped_rgb, 
                             (af_x, af_y - line_length), 
                             (af_x, af_y + line_length), 
                             af_color, 2)

                height, width, channel = display_image.shape
                bytes_per_line = channel * width
                qimage = QImage(display_image.data, width, height, bytes_per_line, QImage.Format_RGB888)
                pixmap = QPixmap.fromImage(qimage)
            
                # Scale image berdasarkan ukuran widget
                if hasattr(self.main_widget, 'is_toupcam_main'):
                    if not self.main_widget.is_toupcam_main:  # Jika preview adalah main
                        scaled_pixmap = pixmap.scaled(1050, 850, Qt.KeepAspectRatio)
                    else:  # Jika preview adalah PIP
                        scaled_pixmap = pixmap.scaled(250, 200, Qt.KeepAspectRatio)
                else:
                    scaled_pixmap = pixmap.scaled(self.width(), self.height(), Qt.KeepAspectRatio)
                
                self.setPixmap(scaled_pixmap)
                self.setFixedSize(scaled_pixmap.size())

            except Exception as e:
                print(f"Error in update_crop_display: {e}")

class MainWidget(QWidget):
    
    evtCallback = pyqtSignal(int)
    autofocus_finished = pyqtSignal()

    def __init__(self):
        super().__init__()
        self.setMinimumSize(1024, 768)
        self.hcam = None
        self.timer = QTimer(self)
        self.imgWidth = 0
        self.imgHeight = 0
        self.pData = None
        self.res = 0
        self.temp = toupcam.TOUPCAM_TEMP_DEF
        self.tint = toupcam.TOUPCAM_TINT_DEF
        self.count = 0
        self.is_recording = False
        self.video_writer = None
        self.image_stream_widget = ImageStreamWidget(self)
        self.complex = Multi_AF(self)
        self.is_window_active = False
        self.installEventFilter(self)

        self.current_frame = None
        self.collect_frame = []
        self.focus_scores = []
        self.is_collecting = False
        self.machine_position = [0, 0, 0]  # [X, Y, Z]
        self.frames_for_stitching = []
        self.image_count = 1
        self.roi_corner = None

        self.is_autifocus_complete = False
        self.stop_index = 0
        self.current_index = 0
        self.last_index = 0

        # Initialize components for wgctrl
        gboxres = QGroupBox("Resolution")
        self.cmb_res = QComboBox()
        self.cmb_res.setEnabled(False)
        vlytres = QVBoxLayout()
        vlytres.addWidget(self.cmb_res)
        gboxres.setLayout(vlytres)
        self.cmb_res.currentIndexChanged.connect(self.onResolutionChanged)
        
        gboxexp = QGroupBox("Exposure")
        self.cbox_auto = QCheckBox("Auto exposure")
        self.cbox_auto.setEnabled(False)
        self.lbl_expoTime = QLabel("0")
        self.lbl_expoGain = QLabel("0")
        self.slider_expoTime = QSlider(Qt.Horizontal)
        self.slider_expoGain = QSlider(Qt.Horizontal)
        self.slider_expoTime.setEnabled(False)
        self.slider_expoGain.setEnabled(False)
        self.cbox_auto.stateChanged.connect(self.onAutoExpo)
        self.slider_expoTime.valueChanged.connect(self.onExpoTime)
        self.slider_expoGain.valueChanged.connect(self.onExpoGain)
        vlytexp = QVBoxLayout()
        vlytexp.addWidget(self.cbox_auto)
        vlytexp.addLayout(self.makeLayout(QLabel("Time(us):"), self.slider_expoTime, self.lbl_expoTime, 
                                           QLabel("Gain(%):"), self.slider_expoGain, self.lbl_expoGain))
        gboxexp.setLayout(vlytexp)
        
        gboxwb = QGroupBox("White balance")
        self.btn_autoWB = QPushButton("White balance")
        self.btn_autoWB.setEnabled(False)
        self.btn_autoWB.clicked.connect(self.onAutoWB)
        self.lbl_temp = QLabel(str(toupcam.TOUPCAM_TEMP_DEF))
        self.lbl_tint = QLabel(str(toupcam.TOUPCAM_TINT_DEF))
        self.slider_temp = QSlider(Qt.Horizontal)
        self.slider_tint = QSlider(Qt.Horizontal)
        self.slider_temp.setRange(toupcam.TOUPCAM_TEMP_MIN, toupcam.TOUPCAM_TEMP_MAX)
        self.slider_temp.setValue(toupcam.TOUPCAM_TEMP_DEF)
        self.slider_tint.setRange(toupcam.TOUPCAM_TINT_MIN, toupcam.TOUPCAM_TINT_MAX)
        self.slider_tint.setValue(toupcam.TOUPCAM_TINT_DEF)
        self.slider_temp.setEnabled(False)
        self.slider_tint.setEnabled(False)
        self.slider_temp.valueChanged.connect(self.onWBTemp)
        self.slider_tint.valueChanged.connect(self.onWBTint)
        vlytwb = QVBoxLayout()
        vlytwb.addLayout(self.makeLayout(QLabel("Temperature"), self.slider_temp, self.lbl_temp, 
                                           QLabel("Tint"), self.slider_tint, self.lbl_tint))
        vlytwb.addWidget(self.btn_autoWB)
        gboxwb.setLayout(vlytwb)

        gboxca = QGroupBox("Color Adjustment")
        self.lbl_hue = QLabel(str(toupcam.TOUPCAM_HUE_DEF))
        self.lbl_satur = QLabel(str(toupcam.TOUPCAM_SATURATION_DEF))
        self.lbl_bright = QLabel(str(toupcam.TOUPCAM_BRIGHTNESS_DEF))
        self.lbl_contrast = QLabel(str(toupcam.TOUPCAM_CONTRAST_DEF))
        self.lbl_gamma = QLabel(str(toupcam.TOUPCAM_GAMMA_DEF))
        self.slider_hue = QSlider(Qt.Horizontal)
        self.slider_satur = QSlider(Qt.Horizontal)
        self.slider_bright = QSlider(Qt.Horizontal)
        self.slider_contrast = QSlider(Qt.Horizontal)
        self.slider_gamma = QSlider(Qt.Horizontal)
        self.slider_hue.setRange(toupcam.TOUPCAM_HUE_MIN, toupcam.TOUPCAM_HUE_MAX)
        self.slider_hue.setValue(toupcam.TOUPCAM_HUE_DEF)
        self.slider_satur.setRange(toupcam.TOUPCAM_SATURATION_MIN, toupcam.TOUPCAM_SATURATION_MAX)
        self.slider_satur.setValue(toupcam.TOUPCAM_SATURATION_DEF)
        self.slider_bright.setRange(toupcam.TOUPCAM_BRIGHTNESS_MIN, toupcam.TOUPCAM_BRIGHTNESS_MAX)
        self.slider_bright.setValue(toupcam.TOUPCAM_BRIGHTNESS_DEF)
        self.slider_contrast.setRange(toupcam.TOUPCAM_CONTRAST_MIN, toupcam.TOUPCAM_CONTRAST_MAX)
        self.slider_contrast.setValue(toupcam.TOUPCAM_CONTRAST_DEF)
        self.slider_gamma.setRange(toupcam.TOUPCAM_GAMMA_MIN, toupcam.TOUPCAM_GAMMA_MAX)
        self.slider_gamma.setValue(toupcam.TOUPCAM_GAMMA_DEF)
        self.slider_hue.setEnabled(False)
        self.slider_bright.setEnabled(False)
        self.slider_satur.setEnabled(False)
        self.slider_contrast.setEnabled(False)
        self.slider_gamma.setEnabled(False)
        self.slider_hue.valueChanged.connect(self.onCAHue)
        self.slider_bright.valueChanged.connect(self.onCABright)
        self.slider_satur.valueChanged.connect(self.onCASatur)
        self.slider_contrast.valueChanged.connect(self.onCAContrast)
        self.slider_gamma.valueChanged.connect(self.onCAGamma)
        vlytca = QVBoxLayout()
        vlytca.addLayout(self.makeLayout_5(QLabel("Hue"), self.slider_hue, self.lbl_hue,
                                           QLabel("Saturation"), self.slider_satur, self.lbl_satur,
                                        QLabel("Brightness"), self.slider_bright, self.lbl_bright,
                                        QLabel("Contrast"), self.slider_contrast, self.lbl_contrast,
                                        QLabel("Gamma"), self.slider_gamma, self.lbl_gamma))
 
        gboxca.setLayout(vlytca)

        gboxflip = QGroupBox("Flip")
        self.cbox_h = QCheckBox("Horizontal")
        self.cbox_h.clicked.connect(self.onFlipH)
        self.cbox_v = QCheckBox("Vertical")
        self.cbox_v.clicked.connect(self.onFlipV)
        vlytflip = QVBoxLayout()
        vlytflip.addWidget(self.cbox_h)
        vlytflip.addWidget(self.cbox_v)
        gboxflip.setLayout(vlytflip)

        self.btn_open = QPushButton("Open")
        self.btn_open.clicked.connect(self.onBtnOpen)
        self.btn_snap = QPushButton("Snap")
        self.btn_snap.clicked.connect(self.onBtnSnap)
        self.btn_snap.setEnabled(False)
        self.btn_record = QPushButton("Record")
        self.btn_record.clicked.connect(self.onBtnRecord)
        self.btn_record.setEnabled(False)
        vlytctrl = QVBoxLayout()
        vlytctrl.addWidget(self.btn_open)
        vlytctrl.addWidget(self.btn_snap)
        vlytctrl.addWidget(self.btn_record)
        vlytctrl.addWidget(gboxres)
        vlytctrl.addWidget(gboxexp)
        vlytctrl.addWidget(gboxwb)
        vlytctrl.addWidget(gboxca)
        vlytctrl.addWidget(gboxflip)
        vlytctrl.addStretch()
        wgctrl = QWidget()
        wgctrl.setLayout(vlytctrl)

        # Tampilan VIDEO Kamera
        # Buat device widget dan layout

        # Setup main video label
        self.device_layout = QVBoxLayout()

        # Setup main video label
        self.lbl_frame = QLabel()
        self.lbl_video = QLabel()
        self.lbl_video.setFixedSize(1050, 850)

        # Setup PIP video label
        self.image_stream_widget = ImageStreamWidget(self)
        self.image_stream_widget.setFixedSize(320, 240)

        # Container untuk video utama
        self.main_video_container = QWidget()
        self.main_video_layout = QVBoxLayout(self.main_video_container)
        self.main_video_layout.setContentsMargins(0, 0, 0, 0)
        self.main_video_layout.addWidget(self.lbl_video)
        self.main_video_layout.setAlignment(Qt.AlignCenter)
        self.main_video_container.setFixedSize(1050, 850)

        # Container untuk PIP
        self.pip_video_container = QWidget(self.main_video_container)
        self.pip_video_container.mousePressEvent = self.swap_pip_video
        self.pip_layout = QVBoxLayout(self.pip_video_container)
        self.pip_layout.setContentsMargins(0, 0, 0, 0)
        self.pip_layout.addWidget(self.image_stream_widget)
        self.pip_video_container.setFixedSize(250, 200)
        
        # Set posisi PIP
        self.pip_video_container.setGeometry(QRect(780, 20, 250, 200))
        self.pip_video_container.raise_()

        self.lbl_status = QLabel("Status")
        # Create device widget and add to layout
        device_widget = QWidget()
        device_widget.setLayout(self.device_layout)

        # Tambahkan main container ke device layout
        self.device_layout.addWidget(self.main_video_container)

        gbox_res_prev = QGroupBox()
        self.lbl_res =     QLabel("Resolution      : 1920 x 1080")
        self.lbl_pixel_w = QLabel("Pixel (Width)   : 0")
        self.lbl_pixel_h = QLabel("Pixel (Height)  : 0")
        res_prev = QVBoxLayout()
        res_prev.addWidget(self.lbl_res)
        res_prev.addWidget(self.lbl_pixel_w)
        res_prev.addWidget(self.lbl_pixel_h)

        # Tambahkan setelah gbox_res_prev
        gbox_roi = QGroupBox("Region of Interest (ROI)")
        self.lbl_roi_start = QLabel("Start Position (x,y) : 0, 0")
        self.lbl_roi_end = QLabel("End Position (x,y)   : 0, 0")
        self.lbl_roi_size = QLabel("ROI Size            : 0 x 0")
        self.btn_set_roi = QPushButton("Set ROI")
        self.btn_clear_roi = QPushButton("Clear ROI")
        self.chk_pointer = QCheckBox("Show Pointer")
        self.chk_freeze = QCheckBox("Freeze Image")
        self.chk_freeze.stateChanged.connect(self.toggle_freeze)
        self.chk_pointer.setChecked(True)
        self.chk_pointer.stateChanged.connect(self.toggle_pointer)
        self.btn_set_roi.setCheckable(True)
        self.btn_set_roi.clicked.connect(self.toggle_roi_selection)
        self.btn_clear_roi.clicked.connect(self.clear_roi)

        self.btn_save_roi = QPushButton("Save ROI")  # Tambahkan button save
        self.btn_back_to_stream = QPushButton("Back to Stream")  # Tambahkan button kembali
        
        self.btn_save_roi.clicked.connect(self.save_roi_image)
        self.btn_back_to_stream.clicked.connect(self.back_to_stream)
        
        roi_layout = QVBoxLayout()
        roi_layout.addWidget(self.lbl_roi_start)
        roi_layout.addWidget(self.lbl_roi_end)
        roi_layout.addWidget(self.lbl_roi_size)
        roi_layout.addWidget(self.chk_pointer)
        roi_layout.addWidget(self.chk_freeze)  # Tambahkan checkbox freeze
        roi_layout.addWidget(self.btn_set_roi)
        roi_layout.addWidget(self.btn_clear_roi)
        roi_layout.addWidget(self.btn_save_roi)
        roi_layout.addWidget(self.btn_back_to_stream)
        gbox_roi.setLayout(roi_layout)

        gbox_AF = QGroupBox("Auto Focus")
        gbox_AF.setObjectName("gbox_AF")
        self.btn_AF = QPushButton("Auto Focus")
        self.btn_AF.clicked.connect(self.on_AF_clicked)
        self.btn_Multi_AF = QPushButton("Multi Auto Focus")
        self.btn_Multi_AF.clicked.connect(self.multi_AF)
        self.btn_Stitch = QPushButton("Stitch Image")
        self.btn_Stitch.clicked.connect(self.start_capture)
        self.lbl_pointer = QLabel("Pointer")
        self.lbl_pointer.setAlignment(Qt.AlignCenter)
        self.lbl_pointer_value = QLabel("Pixel Pointer : 0,0")
        self.lbl_pointer_value_m = QLabel("Machine Pointer : 0,0")
        self.btn_set_AF = QPushButton("Set Auto Focus Point")
        self.btn_set_AF.clicked.connect(self.set_AF_points)
        vlyt_AF = QVBoxLayout()
        vlyt_AF.addWidget(self.btn_AF)
        vlyt_AF.addWidget(self.btn_Multi_AF)
        vlyt_AF.addWidget(self.btn_Stitch)
        vlyt_AF.addWidget(self.lbl_pointer)
        vlyt_AF.addWidget(self.lbl_pointer_value)
        vlyt_AF.addWidget(self.lbl_pointer_value_m)
        vlyt_AF.addWidget(self.btn_set_AF)
        gbox_AF.setLayout(vlyt_AF)

        # Update layout yang ada
        res_prev = QVBoxLayout()
        res_prev.addWidget(self.lbl_res)
        res_prev.addWidget(self.lbl_pixel_w)
        res_prev.addWidget(self.lbl_pixel_h)
        gbox_res_prev.setLayout(res_prev)

        self.btn_snap_pointer = QPushButton("Snap Pointer")
        self.btn_snap_pointer.clicked.connect(self.on_snap_pointer)
        self.btn_move_to = QPushButton("Move To")
        self.btn_move_to.setCheckable(True)
        self.btn_move_to.clicked.connect(self.toggle_move_to)

        self.btn_preview = QPushButton("Open Preview")
        self.btn_preview.clicked.connect(self.image_stream_widget.start_stream)
        vlytprv = QVBoxLayout()
        vlytprv.addWidget(self.btn_preview)
        vlytprv.addWidget(gbox_res_prev)
        vlytprv.addWidget(gbox_roi)
        vlytprv.addWidget(gbox_AF)
        vlytprv.addWidget(self.btn_snap_pointer)
        vlytprv.addWidget(self.btn_move_to)
        vlytprv.addWidget(self.lbl_status)
        vlytprv.addStretch()
        camera_preview = QWidget()
        camera_preview.setLayout(vlytprv)

        # Create the QTabWidgets
        tab_widget1 = QTabWidget()
        tab_widget1.addTab(wgctrl, "Control Camera")
        tab_widget1.addTab(camera_preview, "Camera Preview")

        self.tab_widget2 = QTabWidget()
        self.tab_widget2.addTab(device_widget, "Device")

        self.is_toupcam_main = True
        self.image_stream_widget.start_stream()

        # Create a new tab for the third QTabWidget
        tab_widget3 = QTabWidget()

        tab_grbl = QWidget()
        self.btn_connect = QPushButton(tab_grbl)
        self.btn_connect.setText("Connect")
        self.btn_connect.clicked.connect(self.connect)
        self.btn_connect.setGeometry(QRect(10, 20, 151, 31))
        self.btn_disconnect = QPushButton(tab_grbl)
        self.btn_disconnect.setText("Disconnect")
        self.btn_disconnect.clicked.connect(self.disconnect)
        self.btn_disconnect.setGeometry(QRect(10, 60, 151, 28))
        self.btn_disconnect.setEnabled(False)
        self.btn_reset = QPushButton(tab_grbl)
        self.btn_reset.setText("Reset")
        self.btn_reset.clicked.connect(self.reset)
        self.btn_reset.setGeometry(QRect(190, 60, 151, 28))
        self.btn_reset.setEnabled(False)
        self.com_port = QComboBox(tab_grbl)
        self.update_ports()
        self.com_port.setGeometry(190, 20, 151, 31)

        gbmm = QGroupBox(tab_grbl)
        gbmm.setTitle("Machine Move")
        gbmm.setGeometry(10, 100, 331, 441)
        self.lbl_jog = QLabel(gbmm)
        self.lbl_jog.setText("Joging Move")
        self.lbl_jog.setGeometry(QRect(10, 30, 101, 16))
        self.btn_jog_y_plus = QPushButton(gbmm)
        self.btn_jog_y_plus.setText("Y +")
        self.btn_jog_y_plus.setGeometry(95, 60, 61, 41)
        self.btn_jog_y_plus.setCheckable(True)
        self.btn_jog_y_plus.pressed.connect( self.true_y_plus)
        self.btn_jog_y_plus.setEnabled(False)
        self.btn_jog_y_minus = QPushButton(gbmm)
        self.btn_jog_y_minus.setText("Y -")
        self.btn_jog_y_minus.setGeometry(95, 120, 61, 41)
        self.btn_jog_y_minus.setCheckable(True)
        self.btn_jog_y_minus.pressed.connect(self.true_y_minus)
        self.btn_jog_y_minus.released.connect(self.stop_jog)
        self.btn_jog_y_minus.setEnabled(False)
        self.btn_jog_x_plus = QPushButton(gbmm)
        self.btn_jog_x_plus.setText("X +")
        self.btn_jog_x_plus.setGeometry(15, 90, 61, 41)
        self.btn_jog_x_plus.pressed.connect(self.true_x_plus)
        self.btn_jog_x_plus.released.connect(self.stop_jog)
        self.btn_jog_x_plus.setEnabled(False)
        self.btn_jog_x_minus = QPushButton(gbmm)
        self.btn_jog_x_minus.setText("X -")
        self.btn_jog_x_minus.setGeometry(175, 90, 61, 41)
        self.btn_jog_x_minus.pressed.connect(self.true_x_minus)
        self.btn_jog_x_minus.released.connect(self.stop_jog)
        self.btn_jog_x_minus.setEnabled(False)
        self.btn_jog_z_plus = QPushButton(gbmm)
        self.btn_jog_z_plus.setText("Z +")
        self.btn_jog_z_plus.setGeometry(255, 60, 61, 41)
        self.btn_jog_z_plus.pressed.connect(self.true_z_plus)
        self.btn_jog_z_plus.released.connect(self.stop_jog)
        self.btn_jog_z_plus.setEnabled(False)
        self.btn_jog_z_minus = QPushButton(gbmm)
        self.btn_jog_z_minus.setText("Z -")
        self.btn_jog_z_minus.setGeometry(255, 120, 61, 41)
        self.btn_jog_z_minus.pressed.connect(self.true_z_minus)
        self.btn_jog_z_minus.released.connect(self.stop_jog)
        self.btn_jog_z_minus.setEnabled(False)

        self.lbl_G01 = QLabel(gbmm)
        self.lbl_G01.setText("G01 Move")
        self.lbl_G01.setGeometry(QRect(10, 170, 81, 16))
        self.btn_gx_plus = QPushButton(gbmm)
        self.btn_gx_plus.setText("X +")
        self.btn_gx_plus.clicked.connect(self.g01_x_plus)
        self.btn_gx_plus.setGeometry(QRect(20, 200, 61, 41))
        self.btn_gx_plus.setEnabled(False)
        self.btn_gx_minus = QPushButton(gbmm)
        self.btn_gx_minus.setText("X -")
        self.btn_gx_minus.clicked.connect(self.g01_x_minus)
        self.btn_gx_minus.setGeometry(QRect(110, 200, 61, 41))
        self.btn_gx_minus.setEnabled(False)
        self.btn_gy_plus = QPushButton(gbmm)
        self.btn_gy_plus.setText("Y +")
        self.btn_gy_plus.clicked.connect(self.g01_y_plus)
        self.btn_gy_plus.setGeometry(QRect(20, 250, 61, 41))
        self.btn_gy_plus.setEnabled(False)
        self.btn_gy_minus = QPushButton(gbmm)
        self.btn_gy_minus.setText("Y -")
        self.btn_gy_minus.clicked.connect(self.g01_y_minus)
        self.btn_gy_minus.setGeometry(QRect(110, 250, 61, 41))
        self.btn_gy_minus.setEnabled(False)
        self.btn_gz_plus = QPushButton(gbmm)
        self.btn_gz_plus.setText("Z +")
        self.btn_gz_plus.clicked.connect(self.g01_z_plus)
        self.btn_gz_plus.setGeometry(QRect(20, 300, 61, 41))
        self.btn_gz_plus.setEnabled(False)
        self.btn_gz_minus = QPushButton(gbmm)
        self.btn_gz_minus.setText("Z -")
        self.btn_gz_minus.clicked.connect(self.g01_z_minus)
        self.btn_gz_minus.setGeometry(QRect(110, 300, 61, 41))
        self.btn_gz_minus.setEnabled(False)

        self.spinbox_gx = QSpinBox(gbmm)
        self.spinbox_gx.setGeometry(QRect(240, 200, 61, 41))
        self.spinbox_gx.setEnabled(False)
        font = QFont()
        font.setPointSize(11)
        self.spinbox_gx.setFont(font)
        self.spinbox_gy = QSpinBox(gbmm)
        self.spinbox_gy.setGeometry(QRect(240, 250, 61, 41))
        self.spinbox_gy.setEnabled(False)
        font = QFont()
        font.setPointSize(11)
        self.spinbox_gy.setFont(font)
        self.spinbox_gz = QSpinBox(gbmm)
        self.spinbox_gz.setGeometry(QRect(240, 300, 61, 41))
        self.spinbox_gz.setEnabled(False)
        font = QFont()
        font.setPointSize(11)
        self.spinbox_gz.setFont(font)
        self.slider_feedrate = QSlider(gbmm)
        self.slider_feedrate.setGeometry(QRect(10, 400, 291, 22))
        self.slider_feedrate.setEnabled(False)
        self.slider_feedrate.setOrientation(Qt.Horizontal)
        self.slider_feedrate.setRange(0, 100)
        self.slider_feedrate.setValue(50)
        self.lbl_feedrate = QLabel(gbmm)
        self.lbl_feedrate.setText("Feedrate")
        self.lbl_feedrate.setGeometry(QRect(10, 370, 161, 16))
        self.lbl_Vfeedrate = QLabel(gbmm)
        value = self.slider_feedrate.value()
        self.lbl_Vfeedrate.setText(str(value))
        self.lbl_Vfeedrate.setGeometry(QRect(250, 370, 55, 16))
        self.lbl_Vfeedrate.setLayoutDirection(Qt.LeftToRight)
        self.lbl_Vfeedrate.setAlignment(Qt.AlignRight|Qt.AlignTrailing|Qt.AlignVCenter)
        self.lbl_Vfeedrate.setObjectName("Lbl_Vfeedrate")

        self.btn_home = QPushButton(tab_grbl)
        self.btn_home.setGeometry(QRect(12, 550, 331, 28))
        self.btn_home.setText("Home")
        self.btn_home.clicked.connect(self.home)
        self.btn_home.setEnabled(False)
        self.btn_hold = QPushButton(tab_grbl)
        self.btn_hold.setGeometry(QRect(10, 590, 151, 28))
        self.btn_hold.setText("hold")
        self.btn_hold.clicked.connect(self.hold)
        self.btn_hold.setEnabled(False)
        self.btn_resume = QPushButton(tab_grbl)
        self.btn_resume.setGeometry(QRect(190, 590, 151, 28))
        self.btn_resume.setText("Resume")
        self.btn_resume.clicked.connect(self.resume)
        self.btn_resume.setEnabled(False)
        self.btn_kill_alarm = QPushButton(tab_grbl)
        self.btn_kill_alarm.setGeometry(QRect(10, 630, 151, 28))
        self.btn_kill_alarm.setText("Kill Alarm")
        self.btn_kill_alarm.clicked.connect(self.kill_alarm)
        self.btn_kill_alarm.setEnabled(False)
        self.btn_lamp = QPushButton(tab_grbl)
        self.btn_lamp.setGeometry(QRect(190, 630, 151, 28))
        self.btn_lamp.setText("Lamp")
        self.btn_lamp.clicked.connect(self.switch)
        self.btn_lamp.setEnabled(False)
        self.btn_send_code = QPushButton(tab_grbl)
        self.btn_send_code.setGeometry(270, 690, 70, 28)
        self.btn_send_code.setText("Send")
        self.btn_send_code.clicked.connect(self.send_code)
        self.btn_send_code.setShortcut("Return")
        self.btn_send_code.setEnabled(False)
        self.command_text = QLineEdit(tab_grbl)
        self.command_text.setGeometry(10, 690, 240, 28)
        self.command_text.setEnabled(False)
        self.console_text = QTextEdit(tab_grbl)
        self.console_text.setGeometry(10, 730, 330, 150)
        self.console_text.setEnabled(False)

        tab_setting = QWidget()
        self.lbl_x = QLabel(tab_setting)
        self.lbl_x.setFont(QFont('Times',10))
        self.lbl_x.setText("X Max Travel")
        self.lbl_x.setGeometry(QRect(30,30,100,20))
        self.lbl_y = QLabel(tab_setting)
        self.lbl_y.setFont(QFont('Times', 10))
        self.lbl_y.setText("Y Max Travel")
        self.lbl_y.setGeometry(QRect(30, 80, 100, 20))
        self.lbl_z = QLabel(tab_setting)
        self.lbl_z.setFont(QFont('Times', 10))
        self.lbl_z.setText("Z Max Travel")
        self.lbl_z.setGeometry(QRect(30, 130, 100, 20))

        self.btn_x = QPushButton(tab_setting)
        self.btn_x.setFont(QFont('Time', 10))
        self.btn_x.setText("") 
        self.btn_x.clicked.connect(self.inpt_x)
        self.btn_x.setGeometry(QRect(275,30, 50, 30))
        self.btn_y = QPushButton(tab_setting)
        self.btn_y.setFont(QFont('Time', 10))
        self.btn_y.setText('')
        self.btn_y.clicked.connect(self.inpt_y)
        self.btn_y.setGeometry(QRect(275, 80, 50, 30))
        self.btn_z = QPushButton(tab_setting)
        self.btn_z.setFont(QFont('Times', 10))
        self.btn_z.setText('')
        self.btn_z.clicked.connect(self.inpt_z)
        self.btn_z.setGeometry(QRect(275,130,50,30))

        self.lbl_back_x = QLabel(tab_setting)
        self.lbl_back_x.setFont(QFont('Times', 10))
        self.lbl_back_x.setText('X Backlash Compensation')
        self.lbl_back_x.setGeometry(QRect(30,180,200,20))
        self.lbl_back_y = QLabel(tab_setting)
        self.lbl_back_y.setFont(QFont('Times', 10))
        self.lbl_back_y.setText('Y Backlash Compensation')
        self.lbl_back_y.setGeometry(QRect(30,230,200,20))
        self.lbl_bacK_z = QLabel(tab_setting)
        self.lbl_bacK_z.setFont(QFont('Times', 10))
        self.lbl_bacK_z.setText('Z Backlash Compensation')
        self.lbl_bacK_z.setGeometry(QRect(30,280,200,20))

        self.btn_back_x = QPushButton(tab_setting)
        self.btn_back_x.setFont(QFont('Times', 10))
        self.btn_back_x.setText('')
        self.btn_back_x.clicked.connect(self.inpt_back_x)
        self.btn_back_x.setGeometry(QRect(275, 180, 50, 30))
        self.btn_back_y = QPushButton(tab_setting)
        self.btn_back_y.setFont(QFont('Time', 10))
        self.btn_back_y.setText('')
        self.btn_back_y.clicked.connect(self.inpt_back_y)
        self.btn_back_y.setGeometry(QRect(275,230, 50, 30))
        self.btn_back_z = QPushButton(tab_setting)
        self.btn_back_z.setFont(QFont('Time', 10))
        self.btn_back_z.setText('')
        self.btn_back_z.clicked.connect(self.inpt_back_z)
        self.btn_back_z.setGeometry(QRect(275, 280, 50, 30))

        self.btn_AF = QPushButton(tab_setting)
        self.btn_AF.setText("Auto Focus")
        self.btn_AF.clicked.connect(self.auto_focus)
        self.btn_AF.setGeometry(QRect(30, 330, 100, 30))

        tab_widget3.addTab(tab_grbl, "Control Grbl")
        tab_widget3.addTab(tab_setting,"Setting")

        tab_widget1.setMaximumSize(330, 931)
        self.tab_widget2.setMaximumSize(1200, 931)
        tab_widget3.setMaximumSize(361, 931)

        tab_widget1.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.tab_widget2.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        tab_widget3.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # Create the main layout
        layout_top = QVBoxLayout()
        layout_top.addWidget(tab_widget1)

        main_layout = QHBoxLayout()
        main_layout.addLayout(layout_top)
        main_layout.addWidget(self.tab_widget2)
        main_layout.addWidget(tab_widget3)

        self.setLayout(main_layout)
        self.timer.timeout.connect(self.onTimer)
        self.evtCallback.connect(self.onevtCallback)

    def makeLayout(self, lbl1, sli1, val1, 
                     lbl2, sli2, val2):
        
        hlyt1 = QHBoxLayout()
        hlyt1.addWidget(lbl1)
        hlyt1.addStretch()
        hlyt1.addWidget(val1)

        hlyt2 = QHBoxLayout()
        hlyt2.addWidget(lbl2)
        hlyt2.addStretch()
        hlyt2.addWidget(val2)

        vlyt = QVBoxLayout()
        vlyt.addLayout(hlyt1)
        vlyt.addWidget(sli1)
        vlyt.addLayout(hlyt2)
        vlyt.addWidget(sli2)

        return vlyt
    
    def makeLayout_5(self, lbl1, sli1, val1, 
                     lbl2, sli2, val2,
                     lbl3, sli3, val3,
                     lbl4, sli4, val4,
                     lbl5, sli5, val5,):
        
        hlyt1 = QHBoxLayout()
        hlyt1.addWidget(lbl1)
        hlyt1.addStretch()
        hlyt1.addWidget(val1)

        hlyt2 = QHBoxLayout()
        hlyt2.addWidget(lbl2)
        hlyt2.addStretch()
        hlyt2.addWidget(val2)

        hlyt3 = QHBoxLayout()
        hlyt3.addWidget(lbl3)
        hlyt3.addStretch()
        hlyt3.addWidget(val3)

        hlyt4 = QHBoxLayout()
        hlyt4.addWidget(lbl4)
        hlyt4.addStretch()
        hlyt4.addWidget(val4)

        hlyt5 = QHBoxLayout()
        hlyt5.addWidget(lbl5)
        hlyt5.addStretch()
        hlyt5.addWidget(val5)

        vlyt = QVBoxLayout()
        vlyt.addLayout(hlyt1)
        vlyt.addWidget(sli1)
        vlyt.addLayout(hlyt2)
        vlyt.addWidget(sli2)
        vlyt.addLayout(hlyt3)
        vlyt.addWidget(sli3)
        vlyt.addLayout(hlyt4)
        vlyt.addWidget(sli4)
        vlyt.addLayout(hlyt5)
        vlyt.addWidget(sli5)
        
        return vlyt
    
    def eventFilter(self, obj, event):
        """Handle window focus events"""
        if event.type() == event.WindowActivate:
            self.is_window_active = True
            keyboard.hook(self.on_key_event)
        elif event.type() == event.WindowDeactivate:
            self.is_window_active = False
            keyboard.unhook_all()
        return super().eventFilter(obj, event)
    
    def swap_pip_video(self, event):
        if event.button() == Qt.LeftButton:
            # Simpan referensi widget sementara
            main_widget = self.main_video_layout.itemAt(0).widget()
            pip_widget = self.pip_layout.itemAt(0).widget()
            
            # Hapus widget dari layout mereka
            self.main_video_layout.removeWidget(main_widget)
            self.pip_layout.removeWidget(pip_widget)
            
            # Tukar ukuran dan atur layout
            if self.is_toupcam_main:
            # Toupcam menjadi PIP
                main_widget.setFixedSize(250, 200)
                pip_widget.setFixedSize(1050, 850)
            
            # Update ukuran container
                self.main_video_container.setFixedSize(1050, 850)
                self.pip_video_container.setFixedSize(250, 200)
                
                # Atur posisi PIP
                self.pip_video_container.setGeometry(QRect(780, 20, 250, 200))
                
            # Update scaling untuk video
                if hasattr(self, 'image_stream_widget'):
                    self.image_stream_widget.setFixedSize(1050, 850)
                if hasattr(self, 'lbl_video'):
                    self.lbl_video.setFixedSize(250,200)
            else:
            # Preview menjadi PIP
                main_widget.setFixedSize(1050, 850)
                pip_widget.setFixedSize(250, 200)
            
            # Update ukuran container
                self.main_video_container.setFixedSize(1050, 850)
                self.pip_video_container.setFixedSize(250, 200)
                
            # Atur posisi PIP
                self.pip_video_container.setGeometry(QRect(780, 20, 250, 200))
                
            # Update scaling untuk video
                if hasattr(self, 'image_stream_widget'):
                    self.image_stream_widget.setFixedSize(250, 200)
                if hasattr(self, 'lbl_video'):
                    self.lbl_video.setFixedSize(1050, 850)
        
        # Tambahkan widget ke layout baru dengan alignment
            self.main_video_layout.addWidget(pip_widget, alignment=Qt.AlignCenter)
            self.pip_layout.addWidget(main_widget, alignment=Qt.AlignCenter)
        
        # Pastikan PIP tetap di atas
            self.pip_video_container.raise_()
        
        # Update flag
            self.is_toupcam_main = not self.is_toupcam_main
        
        # Force layout update
            self.main_video_layout.update()
            self.pip_layout.update()
            self.main_video_container.update()
            self.pip_video_container.update()
            self.tab_widget2.update()

        # Trigger resize event untuk memastikan scaling yang tepat
            QApplication.processEvents()
    
    def toggle_freeze(self, state):
        """Handler untuk checkbox freeze image"""
        if hasattr(self, 'image_stream_widget'):
            self.image_stream_widget.freeze_image(bool(state))

    def toggle_move_to(self):
        """Toggle mode move to"""
        if hasattr(self, 'image_stream_widget'):
            self.image_stream_widget.move_to_mode = self.btn_move_to.isChecked()
            if self.btn_move_to.isChecked():
                self.btn_move_to.setText("Cancel Move To")
                self.btn_set_roi.setEnabled(False)
            else:
                self.btn_move_to.setText("Move To")
                self.btn_set_roi.setEnabled(True)
    
    def on_snap_pointer(self):
        """Handler untuk button snap pointer ROI"""
        if hasattr(self, 'image_stream_widget'):
            filepath = self.image_stream_widget.snap_pointer_roi()
            if filepath:
                QMessageBox.information(self, "Success", f"ROI image saved to:\n{filepath}")
            else:
                QMessageBox.warning(self, "Error", "Failed to save ROI image")
    
    def set_AF_points(self):
        af_points, ok = QInputDialog.getInt(
            self,
            'Set Auto Focus Points',
            'Enter the number of points for auto focus:',
            value=1,
            min=1,
            max=5
        )
        if ok:
            if hasattr(self, 'af_point_labels'):
                for labels in self.af_point_labels:
                    for label in labels:
                        label.deleteLater()

            self.af_point_labels = []

            if not hasattr(self, 'af_points_layout'):
                self.af_points_layout = QVBoxLayout()
                gbox_AF = self.findChild(QGroupBox, 'gbox_AF')
                if gbox_AF:
                    gbox_AF.layout().addLayout(self.af_points_layout)
            else:
                while self.af_points_layout.count():
                    item = self.af_points_layout.takeAt(0)
                    if item.widget():
                        item.widget().deleteLater()
            
            if hasattr(self.image_stream_widget, 'roi_start') and hasattr(self.image_stream_widget, 'roi_end'):
                if self.image_stream_widget.roi_start and self.image_stream_widget.roi_end:
                    x1, y1 = self.image_stream_widget.roi_start
                    x2,y2 = self.image_stream_widget.roi_end
                    roi_width = abs(x2-x1)
                    roi_height = abs(y2-y1)
                    x_min = min(x1, x2)
                    y_min = min(y1, y2)

                    for i in range(af_points):
                        hlayout = QHBoxLayout()

                        point_number = QLabel(f"Point {i+1}")
                        point_number.setFont(QFont('Arial', 10, QFont.Bold))
                        hlayout.addWidget(point_number)

                        vlayout = QVBoxLayout()                         

                        if af_points == 1:
                            af_x = x_min +roi_width //2
                            af_y = y_min +roi_height //2
                        elif af_points ==2:
                            af_x = x_min + (roi_width //3) * (i+1)
                            af_y = y_min + roi_height //2
                        elif af_points == 3:
                            if i==0:
                                af_x = x_min + roi_width //2
                                af_y = y_min + roi_height //4
                            else:
                                af_x = x_min + (roi_width//3)*i
                                af_y = (y_min + roi_height //3)//4
                        elif af_points == 4:
                            af_x = x_min + (roi_width // 3) * (i % 2 + 1)
                            af_y = y_min + (roi_height // 3) * (i // 2)
                        else:
                            if i==4 :
                                af_x = x_min + roi_width //2
                                af_y = y_min + roi_height //2
                            else:
                                af_x = x_min + (roi_width//3)*(i%2+1)
                                af_y = (y_min + roi_height //3)*(i//2+1)
                        pixel_label = QLabel(f"Pixel AF Point {i+1} : ({af_x}, {af_y})")
                        machine_label = QLabel(f"Machine AF Point {i+1} : (0.000, 0.000)")

                        vlayout.addWidget(pixel_label)
                        vlayout.addWidget(machine_label)

                        hlayout.addLayout(vlayout)

                        self.af_points_layout.addLayout(hlayout)
                        self.af_point_labels.append((pixel_label, machine_label))

                    self.af_points_layout.update()
                else:
                    QMessageBox.warning(self, "Warning", "ROI is not set.")
            else:
                QMessageBox.warning(self, "Warning", "Image stream widget is not initialized.")

    def toggle_pointer(self, state):
        """Toggle visibility of center pointer"""
        if hasattr(self, 'image_stream_widget'):
            self.image_stream_widget.show_pointer = bool(state)
            if self.image_stream_widget.is_showing_crop:
                self.image_stream_widget.update_crop_display()

    def toggle_roi_selection(self):
        if self.btn_set_roi.isChecked():
            self.btn_set_roi.setText("Setting ROI...")
        else:
            self.btn_set_roi.setText("Set ROI")
    
    def clear_roi(self):
        self.image_stream_widget.roi_start = None
        self.image_stream_widget.roi_end = None
        self.lbl_roi_start.setText("Start Position (x,y) : 0, 0")
        self.lbl_roi_end.setText("End Position (x,y)   : 0, 0")
        self.lbl_roi_size.setText("ROI Size            : 0 x 0")
        self.btn_set_roi.setChecked(False)
        self.btn_set_roi.setText("Set ROI")

    def save_roi_image(self):
        """Crop ROI yang dipilih dan tampilkan ke stream preview"""
        if hasattr(self, 'image_stream_widget'):
            # Pastikan ROI sudah dipilih
            if self.image_stream_widget.roi_start and self.image_stream_widget.roi_end:
                self.image_stream_widget.crop_and_display_roi()
                self.btn_set_roi.setChecked(False)
                self.btn_set_roi.setText("Set ROI")
            else:
                print("ROI belum dipilih.")

    def back_to_stream(self):
        """Handler untuk button kembali ke stream"""
        if hasattr(self, 'image_stream_widget'):
            self.image_stream_widget.restart_stream()
            self.btn_set_roi.setEnabled(True)
    
    def swap_main_video(self, event):
        """Tukar posisi video utama dengan PIP"""
        if event.button() == Qt.LeftButton:
            self.swap_videos()

    def closeevent(self, event):
        if hasattr(self, 'image_stream_widget'):
            self.image_stream_widget.stop_stream()
        super().closeevent(event)
    
    def onTimer(self):
        if self.hcam:
            nFrame, nTime, nTotalFrame = self.hcam.get_FrameRate()
            self.lbl_frame.setText("frame = {}, fps = {:.1f}".format(nTotalFrame, nFrame * 1000.0 / nTime))

    def closeCamera(self):
        if self.hcam:
            self.hcam.Close()
        self.hcam = None
        self.pData = None

        self.btn_open.setText("Open")
        self.timer.stop()
        self.lbl_frame.clear()
        self.lbl_video.clear()
        self.cbox_auto.setEnabled(False)
        self.slider_expoGain.setEnabled(False)
        self.slider_expoTime.setEnabled(False)
        self.btn_autoWB.setEnabled(False)
        self.slider_temp.setEnabled(False)
        self.slider_tint.setEnabled(False)
        self.btn_snap.setEnabled(False)
        self.cmb_res.setEnabled(False)
        self.cmb_res.clear()
        self.slider_bright.setEnabled(False)
        self.slider_hue.setEnabled(False)
        self.slider_satur.setEnabled(False)
        self.slider_contrast.setEnabled(False)
        self.slider_gamma.setEnabled(False)

    def closeEvent(self, event):
        self.closeCamera()

    def onResolutionChanged(self, index):
        if self.hcam: #step 1: stop camera
            self.hcam.Stop()
        
        self.res = index
        self.imgWidth = self.cur.model.res[index].width
        self.imgHeight = self.cur.model.res[index].height

        if self.hcam: #step 2: restart camera
            self.hcam.put_eSize(self.res)
            self.startCamera()

    def onAutoExpo(self, state):
        if self.hcam:
            self.hcam.put_AutoExpoEnable(1 if state else 0)
            self.slider_expoTime.setEnabled(not state)
            self.slider_expoGain.setEnabled(not state)

    def onFlipH (self, state):
        if self.hcam:
            self.hcam.put_HFlip(1 if state else 0)
    
    def onFlipV (self, state):
        if self.hcam:
            self.hcam.put_VFlip(1 if state else 0)

    def onExpoTime(self, value):
        if self.hcam:
            self.lbl_expoTime.setText(str(value))
            if not self.cbox_auto.isChecked():
                self.hcam.put_ExpoTime(value)

    def onExpoGain(self, value):
        if self.hcam:
            self.lbl_expoGain.setText(str(value))
            if not self.cbox_auto.isChecked():
                self.hcam.put_ExpoAGain(value)
    
    def onAutoWB(self):
        if self.hcam:
            self.hcam.AwbOnce()

    def wbCallback(nTemp, nTint, self):
        self.slider_temp.setValue(nTemp)
        self.slider_tint.setValue(nTint)

    def onWBTemp(self, value):
        if self.hcam:
            self.temp = value
            self.hcam.put_TempTint(self.temp, self.tint)
            self.lbl_temp.setText(str(value))

    def onWBTint(self, value):
        if self.hcam:
            self.tint = value
            self.hcam.put_TempTint(self.temp, self.tint)
            self.lbl_tint.setText(str(value))
    
    def onCAHue(self, value):
        if self.hcam:
            self.hue = value
            self.hcam.put_Hue(self.hue)
            self.lbl_hue.setText(str(value))
    
    def onCABright(self, value):
        if self.hcam:
            self.bright = value
            self.hcam.put_Brightness(self.bright)
            self.lbl_bright.setText(str(value))

    def onCAContrast(self, value):
        if self.hcam:
            self.contrast = value
            self.hcam.put_Contrast(self.contrast)
            self.lbl_contrast.setText(str(value))

    def onCASatur(self, value):
        if self.hcam:
            self.satur = value
            self.hcam.put_Saturation(self.satur)
            self.lbl_satur.setText(str(value))

    def onCAGamma(self, value):
        if self.hcam:
            self.gamma = value
            self.hcam.put_Gamma(self.gamma)
            self.lbl_gamma.setText(str(value))

    def startCamera(self):
        self.pData = bytes(toupcam.TDIBWIDTHBYTES(self.imgWidth * 24) * self.imgHeight)
        uimin, uimax, uidef = self.hcam.get_ExpTimeRange()
        self.slider_expoTime.setRange(uimin, uimax)
        self.slider_expoTime.setValue(uidef)
        usmin, usmax, usdef = self.hcam.get_ExpoAGainRange()
        self.slider_expoGain.setRange(usmin, usmax)
        self.slider_expoGain.setValue(usdef)
        self.handleExpoEvent()
        self.hcam.put_VFlip(1)
        if self.cur.model.flag & toupcam.TOUPCAM_FLAG_MONO == 0:
            self.handleTempTintEvent()
        try:
            self.hcam.StartPullModeWithCallback(self.eventCallBack, self)
        except toupcam.HRESULTException:
            self.closeCamera()
            QMessageBox.warning(self, "Warning", "Failed to start camera.")
        else:
            self.cmb_res.setEnabled(True)
            self.cbox_auto.setEnabled(True)
            self.btn_autoWB.setEnabled(self.cur.model.flag & toupcam.TOUPCAM_FLAG_MONO == 0)
            self.slider_temp.setEnabled(self.cur.model.flag & toupcam.TOUPCAM_FLAG_MONO == 0)
            self.slider_tint.setEnabled(self.cur.model.flag & toupcam.TOUPCAM_FLAG_MONO == 0)
            self.btn_open.setText("Close")
            self.btn_snap.setEnabled(True)
            bAuto = self.hcam.get_AutoExpoEnable()
            self.cbox_auto.setChecked(1 == bAuto)
            self.timer.start(1000)
            self.slider_hue.setEnabled(True)
            self.slider_bright.setEnabled(True)
            self.slider_satur.setEnabled(True)
            self.slider_contrast.setEnabled(True)
            self.slider_gamma.setEnabled(True)

    def openCamera(self):
        self.hcam = toupcam.Toupcam.Open(self.cur.id)
        if self.hcam:
            self.res = self.hcam.get_eSize()
            self.imgWidth = self.cur.model.res[self.res].width
            self.imgHeight = self.cur.model.res[self.res].height
            with QSignalBlocker(self.cmb_res):
                self.cmb_res.clear()
                for i in range(0, self.cur.model.preview):
                    self.cmb_res.addItem("{}*{}".format(self.cur.model.res[i].width, self.cur.model.res[i].height))
                self.cmb_res.setCurrentIndex(self.res)
                self.cmb_res.setEnabled(True)
            self.hcam.put_Option(toupcam.TOUPCAM_OPTION_BYTEORDER, 0) #Qimage use RGB byte order
            self.hcam.put_AutoExpoEnable(1)
            self.startCamera()

    def onBtnOpen(self):
        if self.hcam:
            self.closeCamera()
        else:
            arr = toupcam.Toupcam.EnumV2()
            if 0 == len(arr):
                QMessageBox.warning(self, "Warning", "No camera found.")
            elif 1 == len(arr):
                self.cur = arr[0]
                self.openCamera()
            else:
                menu = QMenu()
                for i in range(0, len(arr)):
                    action = QAction(arr[i].displayname, self)
                    action.setData(i)
                    menu.addAction(action)
                action = menu.exec(self.mapToGlobal(self.btn_open.pos()))
                if action:
                    self.cur = arr[action.data()]
                    self.openCamera()

    def onBtnSnap(self):
        if self.hcam:
            if 0 == self.cur.model.still:    # not support still image capture
                if self.pData is not None:
                    image = QImage(self.pData, self.imgWidth, self.imgHeight, QImage.Format_RGB888)
                    self.count += 1
                    image.save("pyqt{}.jpg".format(self.count))
                    self.tab_widget2.addTab(image, "image ke {}".format(self.count))
            else:
                menu = QMenu()
                for i in range(0, self.cur.model.still):
                    action = QAction("{}*{}".format(self.cur.model.res[i].width, self.cur.model.res[i].height), self)
                    action.setData(i)
                    menu.addAction(action)
                action = menu.exec(self.mapToGlobal(self.btn_snap.pos()))
                self.hcam.Snap(action.data())

    def onBtnRecord(self):
        if self.hcam:
            if 0 == self.cur.model.still:    # not support still image capture
                if self.pData is not None:
                    image = QImage(self.pData, self.imgWidth, self.imgHeight, QImage.Format_RGB888)
                    self.count += 1
                    image.save("pyqt{}.jpg".format(self.count))
                    newimage = image.scaled(self.lbl_video.width(), self.lbl_video.height(), Qt.KeepAspectRatio, Qt.FastTransformation)
                    self.tab_widget2.addTab(newimage, "image ke {}".format(self.count))
            else:
                menu = QMenu()
                for i in range(0, self.cur.model.still):
                    action = QAction("{}*{}".format(self.cur.model.res[i].width, self.cur.model.res[i].height), self)
                    action.setData(i)
                    menu.addAction(action)
                action = menu.exec(self.mapToGlobal(self.btn_snap.pos()))
                self.hcam.Snap(action.data())

    @staticmethod
    def eventCallBack(nEvent, self):
        '''callbacks come from toupcam.dll/so internal threads, so we use qt signal to post this event to the UI thread'''
        self.evtCallback.emit(nEvent)

    def onevtCallback(self, nEvent):
        '''this run in the UI thread'''
        if self.hcam:
            if toupcam.TOUPCAM_EVENT_IMAGE == nEvent:
                self.handleImageEvent()
            elif toupcam.TOUPCAM_EVENT_EXPOSURE == nEvent:
                self.handleExpoEvent()
            elif toupcam.TOUPCAM_EVENT_TEMPTINT == nEvent:
                self.handleTempTintEvent()
            elif toupcam.TOUPCAM_EVENT_STILLIMAGE == nEvent:
                self.handleStillImageEvent()
            elif toupcam.TOUPCAM_EVENT_ERROR == nEvent:
                self.closeCamera()
                QMessageBox.warning(self, "Warning", "Generic Error.")
            elif toupcam.TOUPCAM_EVENT_STILLIMAGE == nEvent:
                self.closeCamera()
                QMessageBox.warning(self, "Warning", "Camera disconnect.")

    def handleImageEvent(self):
        try:
            self.hcam.PullImageV4(self.pData, 0, 24, 0, None)
            image = QImage(self.pData, self.imgWidth, self.imgHeight, QImage.Format_RGB888)
            self.current_frame = np.frombuffer(self.pData, dtype=np.uint8).reshape((self.imgHeight, self.imgWidth, 3))
            if self.is_toupcam_main:
                newimage = image.scaled(1050, 850, Qt.KeepAspectRatio, Qt.FastTransformation)
            else:
                newimage = image.scaled(250, 200, Qt.KeepAspectRatio, Qt.FastTransformation)
            self.lbl_video.setPixmap(QPixmap.fromImage(newimage))
            self.lbl_video.update()
        except Exception as e:
            print(f"Error in handleImageEvent: {e}")

    def handleExpoEvent(self):
        time = self.hcam.get_ExpoTime()
        gain = self.hcam.get_ExpoAGain()
        with QSignalBlocker(self.slider_expoTime):
            self.slider_expoTime.setValue(time)
        with QSignalBlocker(self.slider_expoGain):
            self.slider_expoGain.setValue(gain)
        self.lbl_expoTime.setText(str(time))
        self.lbl_expoGain.setText(str(gain))

    def handleTempTintEvent(self):
        nTemp, nTint = self.hcam.get_TempTint()
        with QSignalBlocker(self.slider_temp):
            self.slider_temp.setValue(nTemp)
        with QSignalBlocker(self.slider_tint):
            self.slider_tint.setValue(nTint)
        self.lbl_temp.setText(str(nTemp))
        self.lbl_tint.setText(str(nTint))

    def handleColrAdjustEvet(self):
        hue = self.hcam.get_Hue()
        bright = self.hcam.get_Brightness()
        satur = self.hcam.get_Saturation()
        contrast = self.hcam.get_Contrast()
        gamma = self.hcam.get_Gamma()
        with QSignalBlocker(self.slider_hue):
            self.slider_hue.setValue(hue)
        with QSignalBlocker(self.slider_bright):
            self.slider_bright.setValue(bright)
        with QSignalBlocker(self.slider_satur):
            self.slider_satur.setValue(satur)
        with QSignalBlocker(self.slider_contrast):
            self.slider_contrast.setValue(contrast)
        with QSignalBlocker(self.slider_gamma):
            self.slider_gamma.setValue(gamma)
        self.lbl_hue.setText(str(hue))
        self.lbl_bright.setText(str(bright))
        self.lbl_satur.setText(str(satur))
        self.lbl_contrast.setText(str(contrast))
        self.lbl_gamma.setText(str(gamma))

    def handleStillImageEvent(self):
        info = toupcam.ToupcamFrameInfoV3()
        try:
            self.hcam.PullImageV3(None, 1, 24, 0, info) # peek
        except toupcam.HRESULTException:
            pass
        else:
            if info.width > 0 and info.height > 0:
                buf = bytes(toupcam.TDIBWIDTHBYTES(info.width * 24) * info.height)
                try:
                    self.hcam.PullImageV3(buf, 1, 24, 0, info)
                except toupcam.HRESULTException:
                    pass
                else:
                    image = QImage(buf, info.width, info.height, QImage.Format_RGB888)
                    self.count += 1
                    newimage = image.scaled(self.lbl_video.width(), self.lbl_video.height(), Qt.KeepAspectRatio, Qt.FastTransformation)
                    if not self.folder_path:
                        image.save("still_{}.jpg".format(self.count))
                        self.lbl_image = QLabel()
                        self.lbl_image.setFixedSize(1050, 850)
                        vlytshow = QVBoxLayout()
                        vlytshow.addWidget(self.lbl_image, 1)
                        vlytshow.setAlignment(self.lbl_image, Qt.AlignCenter)
                        wgshow = QWidget()
                        wgshow.setLayout(vlytshow)
                        self.lbl_image.setPixmap(QPixmap.fromImage(newimage))
                        self.tab_widget2.addTab(wgshow, "image ke {}".format(self.count))
                    else:
                        filename = os.path.join(self.folder_path, f"image_{self.count:04d}.jpg")
                        image.save(filename)

    def auto_focus(self):
        AF = toupcam.ToupcamFocusMotor(imax=5, imin=0, idef=50, imaxabs=1000, iminabs=0, zoneh=100, zonev=100)
        print(AF)

    def update_ports(self):
        self.com_port.clear()
        ports = serial.tools.list_ports.comports()
        for port in ports :
            self.com_port.addItem(port.device)

    def connect(self):
        try:
            COM = self.com_port.currentText()
            self.serial_port = serial.Serial(COM, 115200, timeout=2)
            time.sleep(2)
            self.serial_port.flushInput()
            self.serial_connected = True
            self.btn_connect.setEnabled(False)
            self.com_port.setEnabled(False)
            self.btn_disconnect.setEnabled(True)
            self.btn_reset.setEnabled(True)
            self.btn_jog_y_minus.setEnabled(True)
            self.btn_jog_y_plus.setEnabled(True)
            self.btn_jog_x_minus.setEnabled(True)
            self.btn_jog_x_plus.setEnabled(True)
            self.btn_jog_z_minus.setEnabled(True)
            self.btn_jog_z_plus.setEnabled(True)
            self.btn_gx_minus.setEnabled(True)
            self.btn_gx_plus.setEnabled(True)
            self.btn_gy_minus.setEnabled(True)
            self.btn_gy_plus.setEnabled(True)
            self.btn_gz_minus.setEnabled(True)
            self.btn_gz_plus.setEnabled(True)
            self.spinbox_gx.setEnabled(True)
            self.spinbox_gy.setEnabled(True)
            self.spinbox_gz.setEnabled(True)
            self.slider_feedrate.setEnabled(True)
            self.btn_home.setEnabled(True)
            self.btn_hold.setEnabled(True)
            self.btn_kill_alarm.setEnabled(True)
            self.btn_resume.setEnabled(True)
            self.btn_lamp.setEnabled(True)
            self.btn_send_code.setEnabled(True)
            self.console_text.setEnabled(True)
            self.command_text.setEnabled(True)
            self.timer = QTimer(self)
            self.timer.timeout.connect(self.status)
            self.timer.start(500)
            self.load_setting_130()
        except Exception as e:
            QMessageBox.warning(self,"Error")
    
    def send_grbl_command(self, command):
        """Kirim perintah ke GRBL"""
        if hasattr(self, 'serial_port') and self.serial_connected:
            try:
                # Tambahkan newline jika belum ada
                if not command.endswith('\n'):
                    command += '\n'
                print(f"Sending to serial: {command}")  # Debug print
                self.serial_port.write(command.encode())
                # Tunggu dan baca respons
                time.sleep(0.1)
                response = self.serial_port.readline().decode().strip()
                print(f"Response: {response}")  # Debug print
                
                # Log command ke console
                self.console_text.append(f"Sent: {command.strip()}")
                self.console_text.append(f"Received: {response}")

                # Flush serial buffer
                self.serial_port.flush()
                return response
            except Exception as e:
                print(f"Error sending command: {e}")
                self.console_text.append(f"Error: {str(e)}")
                return None

    def load_setting_130(self):
        settings = self.send_grbl_command('$$')
        value_130 = self.extract_value(settings, '$130')
        self.btn_x.setText(f"{value_130}")
        value_131 = self.extract_value(settings, '$131')
        self.btn_y.setText(f"{value_131}")
        value_132 = self.extract_value(settings, "$132")
        self.btn_z.setText(f"{value_132}")
        value_160  = self.extract_value(settings, "$160")
        self.btn_back_x.setText(f"{value_160}")
        value_161 = self.extract_value(settings, "$161")
        self.btn_back_y.setText(f"{value_161}")
        value_162 = self.extract_value(settings, "$162")
        self.btn_back_z.setText(f"{value_162}")

    def extract_value(self, settings, target):
        # Ekstrak nilai dari string pengaturan GRBL
        for line in settings.splitlines():
            if line.startswith(target):
                # Ambil nilai bulat dari pengaturan
                return int(float(line.split('=')[1]))
        return "Tidak Ditemukan"

    def disconnect(self):
        if self.serial_connected:
            self.timer.stop()
            self.serial_port.close()
            self.serial_connected = False
            self.btn_connect.setEnabled(True)
            self.com_port.setEnabled(True)
            self.btn_disconnect.setEnabled(False)
            self.btn_reset.setEnabled(False)
            self.btn_jog_y_minus.setEnabled(False)
            self.btn_jog_y_plus.setEnabled(False)
            self.btn_jog_x_minus.setEnabled(False)
            self.btn_jog_x_plus.setEnabled(False)
            self.btn_jog_z_minus.setEnabled(False)
            self.btn_jog_z_plus.setEnabled(False)
            self.btn_gx_minus.setEnabled(False)
            self.btn_gx_plus.setEnabled(False)
            self.btn_gy_minus.setEnabled(False)
            self.btn_gy_plus.setEnabled(False)
            self.btn_gz_minus.setEnabled(False)
            self.btn_gz_plus.setEnabled(False)
            self.spinbox_gx.setEnabled(False)
            self.spinbox_gy.setEnabled(False)
            self.spinbox_gz.setEnabled(False)
            self.slider_feedrate.setEnabled(False)
            self.btn_home.setEnabled(False)
            self.btn_hold.setEnabled(False)
            self.btn_kill_alarm.setEnabled(False)
            self.btn_resume.setEnabled(False)
            self.btn_lamp.setEnabled(False)
            self.btn_send_code.setEnabled(False)
            self.console_text.setEnabled(False)
            self.command_text.setEnabled(False)
        
    def status(self):
        if self.serial_connected and hasattr(self, 'serial_port'):
            try:
                # Cek apakah port masih terbuka
                if not self.serial_port.is_open:
                    self.disconnect()
                    return

                # Tulis command status dengan timeout handling
                self.serial_port.write(b"?\n")
                self.serial_port.flush()
                
            # Baca response dengan timeout
                line = self.serial_port.read_all().decode('utf-8').strip()

                if line.startswith('<'):
                    # Parse posisi dari status
                    parts = line.split('|')
                    if len(parts) > 1:
                        pos_part = (parts[1])[5:]
                        pos = pos_part.split(',')
                        if len(pos) >= 3:
                            x = float(pos[0])
                            y = float(pos[1])
                            z = float(pos[2])
                            # Update posisi di image stream widget
                            if hasattr(self, 'image_stream_widget'):
                                self.image_stream_widget.update_machine_position(x, y, z)
                        
                        # Update status label
                        status_text = f"Status : {parts[0][1:]} \n X : {x} Y : {y} Z : {z}"
                        self.lbl_status.setText(status_text)

            except serial.SerialTimeoutException:
                print("Serial timeout - reconnecting...")
                self.disconnect()
                time.sleep(1)
                self.connect()
            except serial.SerialException as e:
                print(f"Serial error: {e}")
                self.disconnect()
            except Exception as e:
                print(f"Unexpected error in status(): {e}")      
        
    def reset(self):
        self.serial_port.write(b"\x18")

    # Joging Move

    def on_key_event(self, e):
        if not self.is_window_active:
            return
        focused_widget = self.focusWidget()
        if e.name in['tab', 'shift', 'alt', 'ctrl']:
            return
        if isinstance(focused_widget, QSlider):
            return
        if e.name.lower() == 'up':
            if e.event_type == 'down':
                if key_status.get(e.name, False):
                    
                    return
                key_status[e.name] = True
                value = self.slider_feedrate.value()
                jog_command = f"$J=G90 Y65  F{value}\n"
                self.serial_port.write(jog_command.encode())
            elif e.event_type == 'up':
                key_status[e.name] = False
                
                stop = f"\x85\n"
                self.serial_port.write(stop.encode())
        elif e.name.lower() == 'down':
            if e.event_type == 'down':
                if key_status.get(e.name, False):
                    return
                key_status[e.name] = True
                value = self.slider_feedrate.value()
                jog_command = f"$J=G90 Y0  F{value}\n"
                self.serial_port.write(jog_command.encode())
            elif e.event_type == 'up':
                key_status[e.name] = False
               
                stop = f"\x85\n"
                self.serial_port.write(stop.encode())
        elif e.name.lower() == 'right':
            if e.event_type == 'down':
                if key_status.get(e.name, False):
                    return
                key_status[e.name] = True
                value = self.slider_feedrate.value()
                jog_command = f"$J=G90 x75  F{value}\n"
                self.serial_port.write(jog_command.encode())
            elif e.event_type == 'up':
                key_status[e.name] = False
              
                stop = f"\x85\n"
                self.serial_port.write(stop.encode())
        elif e.name.lower() == 'left':
            if e.event_type == 'down':
                if key_status.get(e.name, False):
                    return
                key_status[e.name] = True
                value = self.slider_feedrate.value()
                jog_command = f"$J=G90 x0  F{value}\n"
                self.serial_port.write(jog_command.encode())
            elif e.event_type == 'up':
                key_status[e.name] = False
           
                stop = f"\x85\n"
                self.serial_port.write(stop.encode())
        elif e.name.lower() == 'page up':
            if e.event_type == 'down':
                if key_status.get(e.name, False):
                    return
                key_status[e.name] = True
                value = self.slider_feedrate.value()
                jog_command = f"$J=G90 z11  F{value}\n"
                self.serial_port.write(jog_command.encode())
            elif e.event_type == 'up':
                key_status[e.name] = False
  
                stop = f"\x85\n"
                self.serial_port.write(stop.encode())
        elif e.name.lower() == 'page down':
            if e.event_type == 'down':
                if key_status.get(e.name, False):
                    return
                key_status[e.name] = True
                value = self.slider_feedrate.value()
                jog_command = f"$J=G90 z0  F{value}\n"
                self.serial_port.write(jog_command.encode())
            elif e.event_type == 'up':
                key_status[e.name] = False
        
                stop = f"\x85\n"
                self.serial_port.write(stop.encode())
                
    def true_y_plus(self):
        value = self.slider_feedrate.value()
        jog_command = f"$J=G90 Y65  F100\n"
        self.serial_port.write(jog_command.encode())
        print("Y aktif")

    def stop_jog(self, event=None):
        stop = f"\x85\n"
        self.serial_port.write(stop.encode())

    def true_y_minus(self):                 
        value = self.slider_feedrate.value()
        jog_command = f"$J=G90 Y0  F{value}\n"
        self.serial_port.write(jog_command.encode())

    def false_y_minus(self):
        stop_jog = f"\x85\n"
        self.serial_port.write(stop_jog.encode())

    def true_x_plus(self):
        value = self.slider_feedrate.value()
        jog_command = f"$J=G90 X75  F{value}\n"
        self.serial_port.write(jog_command.encode())
    
    def false_x_plus(self):
        stop_jog = f"\x85\n"
        self.serial_port.write(stop_jog.encode())

    def true_x_minus(self):
        value = self.slider_feedrate.value()
        jog_command = f"$J=G90 X0  F{value}\n"
        self.serial_port.write(jog_command.encode())
    
    def false_x_minus(self):
        stop_jog = f"\x85\n"
        self.serial_port.write(stop_jog.encode())

    def true_z_plus(self):
        value = self.slider_feedrate.value()
        jog_command = f"$J=G90 Z11  F{value}\n"
        self.serial_port.write(jog_command.encode())
    
    def false_z_plus(self):
        stop_jog = f"\x85\n"
        self.serial_port.write(stop_jog.encode())

    def true_z_minus(self):
        value = self.slider_feedrate.value()
        jog_command = f"$J=G90 Z0  F{value}\n"
        self.serial_port.write(jog_command.encode())
    
    def false_z_minus(self):
        stop_jog = f"\x85\n"
        self.serial_port.write(stop_jog.encode())

    # G01 Move

    def alaram_2_massage(self):
        error_massage = QMessageBox()
        error_massage.setIcon(QMessageBox.Critical)
        error_massage.setWindowTitle("Error")
        error_massage.setText("Alarm 2")
        error_massage.setInformativeText("Perhatikan Soft Limit")
        error_massage.setStandardButtons(QMessageBox.Ok)
        error_massage.exec_()

    def g01_x_plus(self):
        value = self.spinbox_gx.value()
        speed = self.slider_feedrate.value()
        line = self.lbl_status.text()
        line1= line.split()
        status = float(line1[5])
        max = float(self.btn_x.text()) 
        gerak = status + float(value)
        if gerak > max:
            self.alaram_2_massage()
        else :
            G01_command = f"G91 G01 X{value} F{speed}"
            self.serial_port.write(G01_command.encode())

    def g01_x_minus(self):
        value = self.spinbox_gx.value()
        speed = self.slider_feedrate.value()
        line = self.lbl_status.text()
        line1 = line.split()
        status = float(line1[5])
        gerak = status - float(value)
        if gerak < 0 :
            self.alaram_2_massage()
        else:
            G01_command = f"G91 G01 X-{value} F{speed}"
            self.serial_port.write(G01_command.encode())
    
    def g01_y_plus(self):
        value = self.spinbox_gy.value()
        speed = self.slider_feedrate.value()
        line = self.lbl_status.text()
        line1= line.split()
        status = float(line1[8])
        max = float(self.btn_y.text())
        gerak = status + float(value)
        if gerak > max:
            self.alaram_2_massage()
        else:
            G01_command = f"G91 G01 Y{value} F{speed}"
            self.serial_port.write(G01_command.encode())

    def g01_y_minus(self):
        value = self.spinbox_gy.value()
        speed = self.slider_feedrate.value()
        line = self.lbl_status.text()
        line1 = line.split()
        status = float(line1[5])
        gerak = status - float(value)
        if gerak < 0 :
            self.alaram_2_massage()
        else:
            G01_command = f"G91 G01 Y-{value} F{speed}"
            self.serial_port.write(G01_command.encode())
    
    def g01_z_plus(self):
        value = self.spinbox_gz.value()
        speed = self.slider_feedrate.value()
        line = self.lbl_status.text()
        line1= line.split()
        status = float(line1[11])
        max = float(self.btn_z.text()) 
        gerak = status + float(value)
        if gerak > max:
            self.alaram_2_massage()
        else:
            G01_command = f"G91 G01 Z{value} F{speed}"
            self.serial_port.write(G01_command.encode())
    
    def g01_z_minus(self):
        value = self.spinbox_gz.value()
        speed = self.slider_feedrate.value()
        line = self.lbl_status.text()
        line1 = line.split()
        status = float(line1[5])
        gerak = status - float(value)
        if gerak < 0 :
            self.alaram_2_massage()
        else :
            G01_command = f"G91 G01 Z-{value} F{speed}"
            self.serial_port.write(G01_command.encode())

    def home(self):
        home_command = f"$H\n"
        self.serial_port.write(home_command.encode())
        time.sleep(1)

        self.h_timer = QTimer(self)
        self.h_timer.timeout.connect(self.check_position)
        self.h_timer.start(100)

    def check_position(self):
        status = self.lbl_status.text()  # Mengambil posisi X dari status
        current_x = float(status.split()[5])
        current_y = float(status.split()[8])
        current_z = float(status.split()[11])
            
        if current_x==0 and current_y==0 and current_z==0:
            self.h_timer.stop()
            self.to_preview()
            
    def to_preview(self): 
        reply = QMessageBox.question(
            self,
            'Camera Preview',
            'Move to Camera Preview after Homing ?\n Machine will move to Camera Preview Position.',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            preview = f"G00 X38.913 Y 56.761 z5.186 f100\n"
            self.serial_port.write(preview.encode())
    
    def hold(self):
        hold_command = f"!\n"
        self.serial_port.write(hold_command.encode())

    def resume(self):
        resume_command = f"~\n"
        self.serial_port.write(resume_command.encode())

    def kill_alarm(self):
        kill_alamr_command = f"$X\n"
        self.serial_port.write(kill_alamr_command.encode())
    
    def switch(self):
        global is_on
        if self.is_on:
            Off_Lamp = f"M9\n"
            self.serial_port.write(Off_Lamp.encode())
            self.is_on = False
        else:
            ON_Lamp = f"M8\n"
            self.serial_port.write(ON_Lamp.encode())
            self.is_on = True 

    def send_code(self):
        code = self.command_text.text()  # Get code from entry field
        try:
            self.serial_port.write(( code + "\n").encode('utf-8'))
            self.console_text.append( '<Send Command> : ' + code + '\n')
            self.command_text.clear()
        except Exception as e:
            QMessageBox.warning(self, "Error")
    
    def inpt_x(self):
        input_x, done1 = QInputDialog.getInt(self,'X Max Travel', 'Input Nilai :')

        if done1:
            value = f"$130 = {input_x}"
            self.serial_port.write(value.encode())
            self.btn_x.setText(str(input_x))

    def inpt_y(self):
        input_y, done1 = QInputDialog.getInt(self, 'Y Max Travel', 'Input Nilai :')

        if done1:
            Value = f"$131 = {input_y}"
            self.serial_port.write(Value.encode())
            self.btn_y.setText(str(input_y))

    def inpt_z(self):
        input_z, done1 = QInputDialog.getInt(self, 'Z Max Travel', 'Input Nilai :')

        if done1 :
            value = f"$132 = {input_z}"
            self.serial_port.write(value.encode())
            self.btn_z.setText(str(input_z))
    
    def inpt_back_x(self):
        input_back_x, done1  = QInputDialog.getInt(self, 'X Backlash Compensation', 'Input Nilai :')

        if done1:
            value = f"$160 = {input_back_x}"
            self.serial_port.write(value.encode())
            self.btn_back_x.setText(str(input_back_x))

    def inpt_back_y(self):
        input_back_y, done1 = QInputDialog.getInt(self, 'Y Backlash Compensation', 'Input Nilai :')

        if done1:
            value = f"$161 = {input_back_y}"
            self.serial_port.write(value.encode())
            self.btn_back_y.setText(str(input_back_y))

    def inpt_back_z(self):
        input_back_z, done1 = QInputDialog.getInt(self, 'Z Backlash Compensation', 'Input Nilai :')

        if done1:
            value = f"$162 = {input_back_z}"
            self.serial_port.write(value.encode())
            self.btn_back_z.setText(str(input_back_z))
    

    def on_AF_clicked(self):
        """Handler untuk tombol Auto Focus"""
        try:
        # Cek apakah kamera dan serial port tersedia
            if not hasattr(self, 'hcam') or not self.hcam:
                QMessageBox.warning(self, "Error", "Camera not initialized")
                return
            
            if not hasattr(self, 'serial_port') or not self.serial_connected:
                QMessageBox.warning(self, "Error", "Machine not connected")
                return
            
        # Konfirmasi dengan user
            reply = QMessageBox.question(
                self, 
                'Start Autofocus',
                'Start autofocus process?\n Machine will move through Z range.',
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
        
            if reply == QMessageBox.Yes:
                # Disable kontrol yang relevan
                self.btn_AF.setEnabled(False)
                self.btn_AF.setText("AF Running...")
            
            # Mulai proses autofocus
                self.start_autofocus()
        
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error starting autofocus: {e}")
            self.stop_autofocus()

    def start_autofocus(self):
        """Start autofocus process"""
        try:
            if not hasattr(self, 'hcam') or not self.hcam:
                print("Camera not initialized")
                return
        
            # Initialize autofocus parameters
            self.is_autofocusing = True  
            self.btn_AF.setEnabled(False)
            self.focus_scores = []  # List untuk menyimpan skor fokus
            self.z_positions = []   # List untuk menyimpan posisi Z
            self.cbt = 0
        
            # Define Z scan parameters
            self.z_start = 8.5
            self.z_end = 9.43
            self.feedrate = 0.1  # mm/sec - kecepatan gerakan Z
        
            # Reset best focus tracking
            self.best_focus_score = float('-inf')
            self.best_focus_z = None
        
            # Move to start position
            print(f'Moving to start position Z = {self.z_start:.3f}')
            self.send_grbl_command(f"G0 Z{self.z_start:.3f}")
            time.sleep(3)  # Wait for movement
        
            # Start continuous movement
            self.send_grbl_command(f"$J=G90 Z9.43  F10")  # Convert feedrate to mm/min
        
            # Start measurement timer - sampling lebih cepat
            self.af_timer = QTimer()
            self.af_timer.timeout.connect(self.measure_focus_continuous)
            self.af_timer.start(50)  # Measure every 50ms

            self.autofocus_finished.emit()
        
        except Exception as e:
            print(f"Error starting autofocus: {e}")
            self.stop_autofocus()
        finally:
            self.is_autofocus_complete = True
    
    def measure_focus_continuous(self):
        """Measure focus during continuous Z movement"""
        try:
            # Get current Z position
            status = self.lbl_status.text()
            current_z = float(status.split()[11])  # Mengambil posisi Z dari status
            
            if current_z >= self.z_end:
                self.finish_autofocus()
                return
                
            # Calculate focus score for current frame
            if hasattr(self, 'current_frame') and self.current_frame is not None:
                focus_score = self.calculate_focus_score(self.current_frame)
                
                # Store results
                self.focus_scores.append(focus_score)
                self.z_positions.append(current_z)
                
                # Update best focus if necessary
                if focus_score > self.best_focus_score:
                    self.best_focus_score = focus_score
                    self.best_focus_z = current_z
                    print(focus_score)
                    print(f"New best focus at Z = {self.best_focus_z:.3f} with score {self.best_focus_score}")

                elif focus_score > 20:
                    print('p')
                    stop_jog = f"\x85\n"
                    self.serial_port.write(stop_jog.encode())
                    self.finishAF()

                
        except Exception as e:
            print(f"Error measuring focus point: {e}")
            self.stop_autofocus()

    def finishAF(self):
        try: 
            if hasattr(self, 'af_timer'):
                self.af_timer.stop()
            
            self.send_grbl_command(f"G91")
            self.send_grbl_command("G90") 
            self.is_autofocusing = False
            self.btn_AF.setEnabled(True)
            self.btn_AF.setText("Auto Focus")
        
            # Plot focus curve

        except Exception as e:
            print(f"Error finishing autofocus: {e}")
            self.stop_autofocus()  
        
        finally:
            self.is_autifocus_complete = True

    def finish_autofocus(self):
        """Complete autofocus process"""
        try:
            # Stop measurement timer
            if hasattr(self, 'af_timer'):
                self.af_timer.stop()
        
            # Stop Z movement
            self.send_grbl_command("G90")  # Switch back to absolute positioning

            # Move to best focus position
            if self.best_focus_z is not None:
                print(f"Moving to best focus at Z = {self.best_focus_z:.3f}")
                self.send_grbl_command(f"G0 Z{self.best_focus_z:.3f}")
        
            # Count occurrences of each Z position
            z_count = {}
            for z in self.z_positions:
                if z in z_count:
                    z_count[z] += 1
                else:
                    z_count[z] = 1
        
            # Print the Z positions and their counts
            print("Z positions and their counts:")
            for z, count in z_count.items():
                print(f"Z = {z:.3f} : {count} times")
        
            # Reset state
            self.is_autofocusing = False
            self.btn_AF.setEnabled(True)
            self.btn_AF.setText("Auto Focus")
        
            # Plot focus curve

        
        except Exception as e:
            print(f"Error finishing autofocus: {e}")
            self.stop_autofocus()
        
        finally:
            self.is_autifocus_complete=True
            
    def stop_autofocus(self):
        """Emergency stop for autofocus process"""
        try:
            if hasattr(self, 'af_timer'):
                self.af_timer.stop()
        
            self.send_grbl_command("!")  # Feed hold
            time.sleep(0.1)
            self.send_grbl_command("$X")  # Kill alarm
        
            self.is_autofocusing = False
            self.btn_AF.setEnabled(True)
            self.btn_AF.setText("Auto Focus")
        
        except Exception as e:
            print(f"Error stopping autofocus: {e}")

    
    def calculate_focus_score(self, frame):
        """Calculate focus score using faster methods"""
        try:
        # Konversi ke grayscale
            if len(frame.shape) == 3:
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            else:
                gray = frame
            
        # Method 1: Sobel dengan downsample
        # Downsample frame untuk kecepatan
            small_frame = cv2.resize(gray, (0,0), fx=0.5, fy=0.5)
        
        # Hitung gradien dengan Sobel (lebih cepat dari Laplacian)
            gx = cv2.Sobel(small_frame, cv2.CV_64F, 1, 0, ksize=3)
            gy = cv2.Sobel(small_frame, cv2.CV_64F, 0, 1, ksize=3)
        
        # Hitung magnitude
            score = np.mean(np.sqrt(gx*gx + gy*gy))
        
            return score

            # Method 2: Simple gradient difference (paling cepat)
            """
            # Hitung perbedaan antar pixel
            dx = np.diff(gray[::2, ::2], axis=1)  # Skip setiap 2 pixel
            dy = np.diff(gray[::2, ::2], axis=0)
        
            # Hitung rata-rata magnitude gradien
            score = np.mean(np.abs(dx)) + np.mean(np.abs(dy))
        
            return score
            """
        
        # Method 3: Menggunakan Scharr operator (lebih akurat dari Sobel, masih cepat)
            """
            gx = cv2.Scharr(gray, cv2.CV_64F, 1, 0)
            gy = cv2.Scharr(gray, cv2.CV_64F, 0, 1)
            score = np.mean(np.sqrt(gx*gx + gy*gy))
        
            return score
            """
        
        except Exception as e:
            print(f"Error calculating focus score: {e}")
            return 0.0

    def start_capture(self):
        """Mulai proses pengambilan gambar"""
        self.reset_image_count()
        self.folder_name = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        os.makedirs(self.folder_name, exist_ok=True)
        try:
            if not hasattr(self, 'hcam') or not self.hcam:
                print("camera not found")
                return
            
            status = self.lbl_status.text()
            self.x_sekarang = float(status.split()[5])
            self.x_start = self.x_sekarang - 1.0  # Atur posisi awal X yang diinginkan
            self.x_end = self.x_sekarang + 1.0    # Atur posisi akhir X yang diinginkan

            # Pindah ke posisi awal
            self.send_grbl_command(f"G1 X{self.x_start:.3f} f50")
            time.sleep(2)  # Tunggu gerakan

            self.current_x = self.x_start  # Set posisi X saat ini
            self.capture_timer = QTimer()
            self.capture_timer.timeout.connect(self.capture_frame)
            self.capture_timer.start(1000)  # Ambil gambar setiap 1 detik

        except Exception as e:
            print(f"Error starting capture: {e}")

    def capture_frame(self):
        try:
            # Get current X position
            status = self.lbl_status.text()  # Mengambil posisi X dari status
            current_x = float(status.split()[5])

            # Periksa apakah sudah mencapai posisi akhir
            if current_x >= self.x_end:
                self.capture_timer.stop()  # Hentikan timer jika sudah mencapai akhir
                self.stitch()
                return

            # Ambil gambar dan simpan
            if hasattr(self, 'current_frame') and self.current_frame is not None:
                # Simpan gambar di sini
                self.save_image(self.current_frame, self.image_count)  # Simpan gambar dengan posisi X
                self.image_count += 1
                # Pindah ke posisi X berikutnya
                self.current_x += 0.5  # Tambah 0.5 ke posisi X saat ini
                self.send_grbl_command(f"G1 X{self.current_x:.3f} f50")  # Gerakan ke posisi X baru
                time.sleep(2)  # Tunggu gerakan sebelum mengambil gambar

        except Exception as e:
            print(f"Error capturing frame: {e}")

    def save_image(self, frame, image_number):
        # Implementasi untuk menyimpan gambar
        
        filename = f"{self.folder_name}/image_{image_number}.jpg"  # Nama file dengan format image_1, image_2, ...
        cv2.imwrite(filename, frame)  # Simpan gambar menggunakan OpenCV
    
    def reset_image_count(self):
        self.image_count = 1
    
    def captureAndSaveImage(self, index):
        if hasattr(self, 'current_frame') and self.current_frame is not None:
            self.save_stitch(self.current_frame, index)
        else:
            print("No frame available for snapshot.")
    
    def folder_path(self):
        folder = QFileDialog.getExistingDirectory(self, "Pilih Folder untuk Menyimpan File")

        if folder:  # Jika user memilih folder
            self.folder_path = folder
            #self.multi_AF.folder_path_display.setText(folder)  # Simpan di QLineEdit juga
            print(f"DEBUG: Folder path tersimpan -> {self.folder_path}")  # Debugging
        else:
            print("DEBUG: Tidak ada folder yang dipilih.") 
    
    def save_stitch(self, frame, image_number):
        # Coba ambil dari self.folder_path, jika None atau kosong ambil dari QLineEdit
        print(self.folder_path)
        filename = os.path.join(self.folder_path, f"image_{image_number:04d}.jpg")
        if self.hcam:
            if 0 == self.cur.model.still:
                image = QImage(self.pData, self.imgWidth, self.imgHeight, QImage.Format_RGB888)
                image.save(filename)
            else:
                highest = 0
                self.hcam.Snap(highest)

    
    def get_saved_images(self):
        # Ambil path folder; jika self.folder_path tidak ada, Anda bisa mengambilnya dari QLineEdit misalnya
        folder = self.folder_path if self.folder_path else self.ui.folderLineEdit.text()
    
        # List untuk menampung file gambar
        image_files = []
    
        # Pastikan folder valid
        if not folder or not os.path.isdir(folder):
            print("Folder tidak valid")
            return image_files
    
        # Loop melalui file-file di dalam folder dan filter berdasarkan ekstensi gambar
        for file in os.listdir(folder):
            if file.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp')):
                full_path = os.path.join(folder, file)
                image_files.append(full_path)
    
        return image_files

    def stitch(self):
        """Stitch images saved in the folder and save the panorama."""
        settings = {"detector": "sift", "confidence_threshold": 0.2} 
        stitcher = Stitcher(**settings) 
        image_files = [os.path.join(self.folder_name, file) for file in os.listdir(self.folder_name) if file.endswith(".jpg")] 
        panorama = stitcher.stitch(image_files) 
        save_path = os.path.join(self.folder_name, "Stitch.jpg") 
        cv2.imwrite(save_path, panorama)

    def multi_AF(self):
        if self.image_stream_widget.roi_start is not None :
            self.roi_start = self.image_stream_widget.roi_start
            self.roi_end = self.image_stream_widget.roi_end
            x_start, y_start = self.roi_start
            x_end, y_end = self.roi_end
            x_1, y_1 = self.image_stream_widget.calculate_machine_movement(x_start,y_start)
            x_2, y_2 = self.image_stream_widget.calculate_machine_movement(x_end, y_start)
            x_3, y_3 = self.image_stream_widget.calculate_machine_movement(x_start, y_end)
            x_4, y_4 = self.image_stream_widget.calculate_machine_movement(x_end, y_end)
            dialog_text = (f"Koordinat Mesin\n\n" 
                           f"Pergerakan Mesin:\nDX = {x_1:.3f}, DY = {y_1:.3f}\n" 
                           f"DX = {x_2:.3f}, DY = {y_2:.3f}\n" 
                           f"DX = {x_3:.3f}, DY = {y_3:.3f}\n" 
                           f"DX = {x_4:.3f}, DY = {y_4:.3f}") 
            dialog = Multi_AF(self, dialog_text)
            dialog.setWindowModality(Qt.NonModal)  # Set Non-Modal to allow interaction with main window
            dialog.setWindowFlags(Qt.Dialog | Qt.WindowMinimizeButtonHint | Qt.WindowCloseButtonHint )  # Add Minimize button
            dialog.show()
        else:
            QMessageBox.warning(self, 'Error','Please Set ROI First')
    
    def convert_movements(self, x, y):
        dx, dy = self.image_stream_widget.calculate_machine_movement(x, y)
        return dx, dy

class Multi_AF(QDialog):
    def __init__(self, parent=None, dialog_text=""):
        super().__init__(parent)
        self.setWindowTitle("Multi Auto Focus")
        self.setFixedSize(800, 600)
        self.main_widget = parent
        self.folder_path = None
        self.currentIndex = 0

        # Create tabs
        self.tab_widget = QTabWidget()
        self.tab1 = QWidget()
        self.tab2 = QWidget()
        self.tab3 = QWidget()

        self.create_tab1()
        self.create_tab2(dialog_text)
        self.create_tab3()

        self.tab_widget.addTab(self.tab1, "Step 1")
        self.tab_widget.addTab(self.tab2, "Step 2")
        self.tab_widget.addTab(self.tab3, "Step 3")

        layout = QVBoxLayout()
        layout.addWidget(self.tab_widget)
        self.setLayout(layout)

        # Gambar awal hanya kotak ROI tanpa titik
        self.update_image([])

    def create_tab1(self):
        main_layout = QHBoxLayout()  # Layout utama, sejajar (horizontal)

    # Bagian kiri: Set AF dan tombol Calculate
        control_layout = QVBoxLayout()
        control_layout.addWidget(QLabel("Set Auto Focus"))

        self.checkboxes = []
        self.button_group = QButtonGroup()
        self.button_group.setExclusive(True)

        for i in range(1, 7):
            checkbox = QCheckBox(f"{i} Points")
            self.checkboxes.append(checkbox)
            self.button_group.addButton(checkbox)
            control_layout.addWidget(checkbox)

        calculate_button = QPushButton("Calculate Auto Focus Points")
        calculate_button.clicked.connect(self.calculate_auto_focus)
        control_layout.addWidget(calculate_button)

        calcu_stitch = QPushButton("Calculate Stitching")
        calcu_stitch.clicked.connect(self.calculate_stitching)
        control_layout.addWidget(calcu_stitch)
        
        self.lbl_image = QLabel("Jumlah Gambar  : ")
        control_layout.addWidget(self.lbl_image)
        self.row_image = QLabel("Row Image      :0")
        self.column_image = QLabel("Column Image  :0")
        control_layout.addWidget(self.row_image)
        control_layout.addWidget(self.column_image)
        main_layout.addLayout(control_layout)  # Tambahkan ke layout utama

    # Bagian kanan: Gambar (lebih besar dari sebelumnya)
        self.image_label = QLabel()
        self.image_label.setFixedSize(300, 300)  # Perbesar ukuran gambar
        main_layout.addWidget(self.image_label)

        self.tab1.setLayout(main_layout)

    def create_tab2(self, dialog_text):
        layout = QVBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)  # Lebih rapat ke atas

        layout.addWidget(QLabel(dialog_text))

        self.result_label = QLabel("Machine Coordinate Result")
        layout.addWidget(self.result_label)

        start_button = QPushButton("Start Auto Focus")
        start_button.clicked.connect(self.start_auto_focus)
        layout.addWidget(start_button)

        self.tab2.setLayout(layout)
    
    def create_tab3(self):
        layout = QVBoxLayout()

        self.folder_path_display = QLineEdit(self)
        self.folder_path_display.setReadOnly(True)  # Agar tidak bisa diketik manual
        layout.addWidget(self.folder_path_display)

    # Tambahan: Tombol untuk memilih folder
        self.btn_browse_folder = QPushButton("Pilih Folder")
        self.btn_browse_folder.clicked.connect(self.browse_folder)
        layout.addWidget(self.btn_browse_folder)

        start_stitch = QPushButton("Start Stitching")
        start_stitch.clicked.connect(self.start_stitching)
        layout.addWidget(start_stitch)
        self.tab3.setLayout(layout)

    def calculate_auto_focus(self):
        checked_button = self.button_group.checkedButton()
        points = int(checked_button.text().split()[0]) if checked_button else 0

        roi_start = self.main_widget.roi_start
        roi_end = self.main_widget.roi_end
        x_start, y_start = roi_start
        x_end, y_end = roi_end

        auto_focus_points = self.distribute_points(x_start, y_start, x_end, y_end, points)
        self.update_image(auto_focus_points)  # Sekarang gambar titiknya
        print(auto_focus_points)

    # Konversi ke koordinat mesin
        self.movements = [self.main_widget.convert_movements(x, y) for x, y in auto_focus_points]

    # Perbarui Machine Coordinate Result
        result_text = "Auto Focus Points (Machine Movements):\n"
        for (x, y), (dx, dy) in zip(auto_focus_points, self.movements):
            result_text += f"({x:.3f}, {y:.3f}) → DX = {dx:.3f}, DY = {dy:.3f}\n"

        self.result_label.setText(result_text)

    def distribute_points(self, x_start, y_start, x_end, y_end, points):
        """ Mendapatkan titik-titik auto focus dalam pola lingkaran """
        auto_focus_points = [] 
        if points == 0: 
            return auto_focus_points  

        # Hitung pusat ROI dan radius lingkaran
        center_x = (x_start + x_end) / 2 
        center_y = (y_start + y_end) / 2 
        radius_x = abs(x_end - x_start) / 4  # Jari-jari ¼ lebar
        radius_y = abs(y_end - y_start) / 4  # Jari-jari ¼ tinggi
        
        for i in range(points): 
            angle = 2 * math.pi * i / points  # Sudut yang merata
            x = center_x + radius_x * math.cos(angle) 
            y = center_y + radius_y * math.sin(angle) 
            auto_focus_points.append((x, y)) 
        
        return auto_focus_points

    def update_image(self, points):
        """ Menggambar kotak ROI dan titik-titik auto focus """
        size = 200
        image = QPixmap(size, size)
        image.fill(Qt.white)
        painter = QPainter(image)

        # Gambar kotak ROI tetap
        pen = QPen(Qt.black)
        pen.setWidth(2)
        painter.setPen(pen)
        painter.drawRect(10, 10, size - 20, size - 20)  # Kotak ROI tetap

        # Gambar titik-titik jika ada
        if points:
            pen.setColor(Qt.red)
            pen.setWidth(5)
            painter.setPen(pen)
            for x, y in points:
                # Transformasi agar titik tetap dalam gambar 200x200
                transformed_x = int(((x - self.main_widget.roi_start[0]) / 
                                     (self.main_widget.roi_end[0] - self.main_widget.roi_start[0])) * (size - 20)) + 10
                transformed_y = int(((y - self.main_widget.roi_start[1]) / 
                                     (self.main_widget.roi_end[1] - self.main_widget.roi_start[1])) * (size - 20)) + 10
                painter.drawPoint(transformed_x, transformed_y)

        painter.end()
        self.image_label.setPixmap(image)

    def start_auto_focus(self):
        if self.currentIndex >= len( self.movements):
            print("Selesai")
            return
        dx, dy = self.movements[self.currentIndex]

        self.move_to(dx, dy)
        QTimer.singleShot(2000, lambda:self.check_idle())

    def move_to(self, dx, dy):
        command = f"G01 X{dx} Y{dy} F100\n"
        self.main_widget.send_grbl_command(command)

    def check_idle(self):
        status = self.main_widget.lbl_status.text()
        if "Idle"  not in status:
            QTimer.singleShot(2000, lambda: self.check_idle( ))
        else:
            QTimer.singleShot(2000, lambda:self.start_AF())

    def start_AF(self):

        self.main_widget.start_autofocus()

        QTimer.singleShot(1000, lambda:self.check_AF())

    def check_AF(self):
        if self.main_widget.is_autofocusing:
            QTimer.singleShot(1000, lambda: self.check_AF())
        else:
            best_z = self.main_widget.best_focus_z
            # Simpan nilai best_z ke dalam list; inisialisasi list jika belum ada
            if not hasattr(self, 'best_z_list'):
                self.best_z_list = []
            self.best_z_list.append(best_z)
        
            # Perbarui tampilan di tab 2 dengan daftar best_z
            result_text = "Best Focus Z values for each coordinate:\n"
            for i, z in enumerate(self.best_z_list, start=1):
                result_text += f"Coordinate {i}: {z:.3f}\n"
            self.result_label.setText(result_text)
        
            self.currentIndex += 1
            QTimer.singleShot(2000, lambda: self.start_auto_focus())

    def calculate_stitching(self):
        dx,dy = self.main_widget.roi_start
        dx1, dy1 = self.main_widget.roi_end

        roi_start = self.main_widget.convert_movements(dx, dy)
        roi_end = self.main_widget.convert_movements(dx1, dy1)

        self.coordinates = self.generate_coordinates(roi_start, roi_end)
        total_coordinates = len(self.coordinates)
        self.lbl_image.setText("Jumlah Gambar : " + str(total_coordinates))

    def generate_coordinates(self, roi_start, roi_end, step_x=0.452, step_y = 0.339):
        x_start, y_start = roi_start
        x_end, y_end = roi_end
        self.row_image.setText("ROW Gambar : " + str(y_end - y_start))

    # Hitung jumlah kolom (berdasarkan sumbu X)
        x_coords = np.arange(x_start, x_end, step_x)
        columns = len(x_coords)
        self.column_image.setText("Colums Gambar : " + str(columns))

    # Hitung jumlah baris (berdasarkan sumbu Y)
        y_coords = np.arange(y_end, y_start, step_y)
        rows = len(y_coords)
        self.row_image.setText("Row Gambar : " + str(rows))

    # Generate koordinat
        coordinates = []
        for y in y_coords:
            for x in x_coords:
                coordinates.append((x, y))

        print(f"Baris: {rows}, Kolom: {columns}")
        return coordinates

    def browse_folder(self):
        self.main_widget.folder_path()

    def get_average_best_z(self):
        if not self.best_z_list:  # Cek jika list kosong
            return None  # Bisa juga return 0 atau nilai default lainnya

        return ((sum(self.best_z_list) / len(self.best_z_list)))

    def start_stitching(self):
        self.currentCoordinatesIndex = 0
        self.stitchingInProgress = True
        self.processNextCoordinate()

    def processNextCoordinate(self):
        if self.currentCoordinatesIndex >= len(self.coordinates):
            self.stitchingInProgress = False
            self.openICE()
            return
        coord = self.coordinates[self.currentCoordinatesIndex]
        z = 9.345
        command = f"G01 X{coord[0]} Y{coord[1]} F100\n"
        self.main_widget.send_grbl_command(command)
        QTimer.singleShot(2000, self.checkStatus)

    def checkStatus(self):

        self.main_widget.send_grbl_command(b"?\n")
        QTimer.singleShot(200, self.processStatusResponse)

    def processStatusResponse(self):
        data = self.main_widget.serial_port.read_all().decode('utf-8', errors='ignore')
        if "Idle" in data:
            QTimer.singleShot(2000, self.captureAndProceed)
        else:
            QTimer.singleShot(1000, self.checkStatus)

    def captureAndProceed(self):
        self.main_widget.captureAndSaveImage(self.currentCoordinatesIndex)
        self.currentCoordinatesIndex += 1
        QTimer.singleShot(2000, self.processNextCoordinate)

    def openICE(self):
        self.gambar = self.main_widget.get_saved_images()
        self.ice_path = r"C:\Program Files\Microsoft Research\Image Composite Editor\ICE.exe"

        if not os.path.exists(self.ice_path):
            QMessageBox.warning(
                self,
                "Peringatan",
                "Microsoft ICE tidak ditemukan. Pilih lokasi file ice.exe secara manual."
            )
            manual_path, _ = QFileDialog.getOpenFileName(
                self,
                "Cari ice.exe",
                "",
                "Executable (*.exe)"
            )
            if manual_path:
                self.ice_path = manual_path
            else:
                return

        # Jalankan Microsoft ICE
        try:
            if self.gambar:
                # Jika ada gambar yang dipilih, buka ICE dengan gambar tersebut
                subprocess.Popen([self.ice_path] +self.gambar)
            else:
                # Jika tidak ada gambar, buka ICE saja
                subprocess.Popen([self.ice_path])
        except Exception as e:
            QMessageBox.critical(
                self,
                "Error",
                f"Gagal membuka Microsoft ICE: {str(e)}"
            )

if __name__ == '__main__':
    app = QApplication(sys.argv)
    mw = MainWidget() 
    mw.show()
    sys.exit(app.exec_())
